<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
            background-attachment: fixed;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .container {
            text-align: center;
            width: 80%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            padding: 20px;
        }
        .video-container {
            margin-bottom: 20px;
            width: 100%;
        }
        video {
            width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .btn-container {
            margin-top: 20px;
        }
        .next-btn {
            padding: 12px 30px;
            font-size: 16px;
            background-color: #cccccc;
            color: #666666;
            border: none;
            border-radius: 4px;
            cursor: not-allowed;
            transition: all 0.3s ease;
        }
        .next-btn.active {
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
        }
        .next-btn.active:hover {
            background-color: #45a049;
            transform: translateY(-2px);
        }
        .hidden {
            display: none;
        }
        .next-page {
            text-align: center;
            width: 90%;
            max-width: 900px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            padding: 20px;
        }
        .backup-container {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 20px;
        }
        .file-system {
            flex: 1;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
        }
        .backup-drive {
            flex: 1;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
        }
        .file-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
        }
        .file-item:hover {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .file-item.selected {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.4);
        }
        .action-buttons {
            margin: 15px 0;
            text-align: center;
        }
        .progress-container {
            margin-top: 20px;
        }
        .progress-bar {
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.5s;
        }
        .status-message {
            font-weight: bold;
            color: #333;
        }
        .complete-btn {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: none;
        }
        .complete-btn:hover {
            background-color: #45a049;
        }
        .context-menu {
            display: none;
            position: absolute;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
            z-index: 1000;
        }
        .context-menu-item {
            padding: 8px 15px;
            cursor: pointer;
        }
        .context-menu-item:hover {
            background-color: #f5f5f5;
        }
        .instruction {
            background: rgba(255, 253, 231, 0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-left: 4px solid #ffd600;
            border: 1px solid rgba(255, 214, 0, 0.3);
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 0 4px 4px 0;
            text-align: left;
        }
        .clipboard-status {
            font-style: italic;
            color: #666;
            margin-top: 10px;
        }
        .drop-zone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: #666;
            margin-top: 10px;
            min-height: 80px;
        }
        .drop-zone.drag-over {
            border-color: #2196F3;
            background-color: #e3f2fd;
        }
        .security-shield {
            position: fixed;
            width: 100px;
            height: 100px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232b50c7"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            opacity: 0;
            z-index: 100;
            filter: drop-shadow(0 0 10px rgba(43, 80, 199, 0.7));
            animation: float 3s ease-in-out infinite;
            display: none;
        }
        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0px) rotate(0deg); }
        }
        .show-shield {
            display: block;
            opacity: 1;
            animation: fadeIn 1s ease-in-out forwards, float 3s ease-in-out infinite;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 添加弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 500px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: modalIn 0.5s ease-out forwards;
            transform: translateY(-50px);
            opacity: 0;
        }
        
        @keyframes modalIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .modal-title {
            color: #2b50c7;
            font-size: 24px;
            margin-bottom: 15px;
        }
        
        .modal-text {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333;
        }
        
        .shield-container {
            margin: 20px auto;
            width: 100px;
            height: 100px;
            position: relative;
        }
        
        .confetti {
            position: fixed;
            width: 10px;
            height: 10px;
            background-color: #f00;
            animation: confetti-fall 3s ease-in-out infinite;
            z-index: 9999;
        }
        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="video-container">
            <video id="myVideo" controls autoplay muted playsinline>
                <source src="../素材/数据的备份.mp4" type="video/mp4">
                您的浏览器不支持视频标签。
            </video>
        </div>
        <div class="btn-container">
            <button id="nextBtn" class="next-btn">下一步</button>
        </div>
    </div>

    <div id="nextPage" class="next-page hidden">
        <h1>数据备份模拟操作</h1>
        <p>请选择小智需要备份的重要文件，将它们从左侧"小智的文件"复制到右侧的"移动硬盘"</p>
        
        <div class="backup-container">
            <div class="file-system">
                <h2>小智的文件</h2>
                <p class="instruction">在需要备份的文件上右击选择"复制"，然后在右侧移动硬盘上右击选择"粘贴"</p>
                <div class="file-item" data-file="旅游照片" data-size="1.2 GB" data-important="true">
                    <span>📁 旅游照片</span>
                    <span>1.2 GB</span>
                </div>
                <div class="file-item" data-file="临时下载" data-size="250 MB" data-important="false">
                    <span>📁 临时下载</span>
                    <span>250 MB</span>
                </div>
                <div class="file-item" data-file="游戏程序" data-size="1.5 GB" data-important="false">
                    <span>🎮 游戏程序</span>
                    <span>1.5 GB</span>
                </div>
                <div class="file-item" data-file="缓存文件" data-size="420 MB" data-important="false">
                    <span>📁 缓存文件</span>
                    <span>420 MB</span>
                </div>
                <div class="file-item" data-file="系统临时文件" data-size="1.8 GB" data-important="false">
                    <span>📁 系统临时文件</span>
                    <span>1.8 GB</span>
                </div>
            </div>
            
            <div class="backup-drive">
                <h2>移动硬盘</h2>
                <p id="emptyMessage">暂无备份文件</p>
                <div id="backupFiles"></div>
                <div id="dropZone" class="drop-zone">在此处右击选择"粘贴"</div>
            </div>
        </div>
        
        <div class="clipboard-status" id="clipboardStatus">剪贴板: 空</div>
        
        <div class="progress-container">
            <div class="progress-bar">
                <div id="progressBar" class="progress"></div>
            </div>
            <p id="statusMessage" class="status-message">请右击文件选择"复制"</p>
            <p>已备份: <span id="backupCount">0</span>/<span id="totalImportantCount">1</span> 个重要文件</p>
            <button id="completeBtn" class="complete-btn">完成备份</button>
        </div>
    </div>
    
    <!-- 右键菜单 -->
    <div id="contextMenu" class="context-menu">
        <div id="copyMenuItem" class="context-menu-item">复制</div>
        <div id="pasteMenuItem" class="context-menu-item">粘贴</div>
    </div>

    <!-- 添加弹窗 -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <div class="modal-title">恭喜你闯关成功！</div>
            <div class="modal-text">你已成功备份了所有重要数据，获得一个数据防护盾！</div>
            <div class="shield-container">
                <div class="security-shield show-shield" id="security-shield"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.getElementById('myVideo');
            const nextBtn = document.getElementById('nextBtn');
            const container = document.querySelector('.container');
            const nextPage = document.getElementById('nextPage');
            
            // 尝试多种方式自动播放视频
            function attemptPlay() {
                // 尝试播放
                var playPromise = video.play();
                
                if (playPromise !== undefined) {
                    playPromise.then(_ => {
                        // 自动播放成功
                        console.log('自动播放成功');
                    })
                    .catch(error => {
                        // 自动播放失败
                        console.log('自动播放失败，需要用户交互:', error);
                        // 添加点击事件，让用户点击页面任意位置开始播放
                        document.addEventListener('click', function startPlayback() {
                            video.play();
                            document.removeEventListener('click', startPlayback);
                        }, { once: true });
                    });
                }
            }
            
            // 页面加载后尝试播放
            attemptPlay();
            
            // 视频结束时激活按钮
            video.addEventListener('ended', function() {
                nextBtn.classList.add('active');
            });
            
            // 点击下一步按钮
            nextBtn.addEventListener('click', function() {
                if (nextBtn.classList.contains('active')) {
                    container.classList.add('hidden');
                    nextPage.classList.remove('hidden');
                    initBackupSimulation();
                }
            });
            
            // 初始化备份模拟
            function initBackupSimulation() {
                const fileItems = document.querySelectorAll('.file-item');
                const backupFiles = document.getElementById('backupFiles');
                const emptyMessage = document.getElementById('emptyMessage');
                const progressBar = document.getElementById('progressBar');
                const statusMessage = document.getElementById('statusMessage');
                const backupCount = document.getElementById('backupCount');
                const completeBtn = document.getElementById('completeBtn');
                const totalImportantCount = document.getElementById('totalImportantCount');
                const clipboardStatus = document.getElementById('clipboardStatus');
                const contextMenu = document.getElementById('contextMenu');
                const copyMenuItem = document.getElementById('copyMenuItem');
                const pasteMenuItem = document.getElementById('pasteMenuItem');
                const dropZone = document.getElementById('dropZone');
                
                let selectedFile = null;
                let selectedFileElement = null;
                let backedUpFiles = [];
                let clipboard = null;
                
                // 必须备份的文件列表
                const requiredFiles = ['旅游照片'];
                
                // 计算重要文件总数
                let importantFilesCount = 0;
                fileItems.forEach(item => {
                    if(item.getAttribute('data-important') === 'true') {
                        importantFilesCount++;
                    }
                });
                totalImportantCount.textContent = importantFilesCount;
                
                // 文件选择
                fileItems.forEach(item => {
                    item.addEventListener('click', function() {
                        // 取消之前的选择
                        fileItems.forEach(f => f.classList.remove('selected'));
                        
                        // 选中当前文件
                        this.classList.add('selected');
                        selectedFile = this.getAttribute('data-file');
                        selectedFileElement = this;
                        statusMessage.textContent = `已选择: ${selectedFile}`;
                    });
                    
                    // 添加右键菜单
                    item.addEventListener('contextmenu', function(e) {
                        e.preventDefault();
                        
                        // 取消之前的选择
                        fileItems.forEach(f => f.classList.remove('selected'));
                        
                        // 选中当前文件
                        this.classList.add('selected');
                        selectedFile = this.getAttribute('data-file');
                        selectedFileElement = this;
                        
                        // 显示右键菜单
                        contextMenu.style.display = 'block';
                        contextMenu.style.left = e.pageX + 'px';
                        contextMenu.style.top = e.pageY + 'px';
                        
                        // 只显示复制选项
                        copyMenuItem.style.display = 'block';
                        pasteMenuItem.style.display = 'none';
                    });
                });
                
                // 备份区域右键菜单
                dropZone.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    
                    // 显示右键菜单
                    contextMenu.style.display = 'block';
                    contextMenu.style.left = e.pageX + 'px';
                    contextMenu.style.top = e.pageY + 'px';
                    
                    // 只显示粘贴选项
                    copyMenuItem.style.display = 'none';
                    pasteMenuItem.style.display = 'block';
                    
                    // 如果剪贴板为空，禁用粘贴
                    if (!clipboard) {
                        pasteMenuItem.style.opacity = '0.5';
                        pasteMenuItem.style.cursor = 'not-allowed';
                    } else {
                        pasteMenuItem.style.opacity = '1';
                        pasteMenuItem.style.cursor = 'pointer';
                    }
                });
                
                // 点击复制菜单项
                copyMenuItem.addEventListener('click', function() {
                    if (selectedFile) {
                        clipboard = {
                            file: selectedFile,
                            size: selectedFileElement.getAttribute('data-size'),
                            important: selectedFileElement.getAttribute('data-important') === 'true'
                        };
                        
                        statusMessage.textContent = `已复制: ${selectedFile}`;
                        clipboardStatus.textContent = `剪贴板: ${selectedFile}`;
                        
                        // 隐藏右键菜单
                        contextMenu.style.display = 'none';
                    }
                });
                
                // 点击粘贴菜单项
                pasteMenuItem.addEventListener('click', function() {
                    if (clipboard && !backedUpFiles.includes(clipboard.file)) {
                        // 隐藏空消息
                        emptyMessage.style.display = 'none';
                        
                        // 隐藏右键菜单
                        contextMenu.style.display = 'none';
                        
                        // 模拟粘贴过程
                        statusMessage.textContent = `正在备份: ${clipboard.file}`;
                        
                        // 模拟进度条
                        let progress = 0;
                        const interval = setInterval(() => {
                            progress += 2;
                            progressBar.style.width = `${progress}%`;
                            
                            if (progress >= 100) {
                                clearInterval(interval);
                                
                                // 创建备份文件元素
                                const backupFile = document.createElement('div');
                                backupFile.className = 'file-item';
                                
                                // 根据文件类型选择图标
                                let icon = '📄';
                                if (clipboard.file.includes('照片')) icon = '📸';
                                else if (clipboard.file.includes('视频')) icon = '🎬';
                                else if (clipboard.file.includes('资料')) icon = '📚';
                                else if (clipboard.file.includes('日记')) icon = '📝';
                                else if (clipboard.file.includes('游戏')) icon = '🎮';
                                else if (clipboard.file.includes('缓存')) icon = '📁';
                                else if (clipboard.file.includes('临时')) icon = '📁';
                                
                                backupFile.innerHTML = `
                                    <span>${icon} ${clipboard.file}</span>
                                    <span>${clipboard.size}</span>
                                `;
                                backupFiles.appendChild(backupFile);
                                
                                // 更新备份状态
                                backedUpFiles.push(clipboard.file);
                                
                                // 如果是重要文件，更新计数
                                if (clipboard.important) {
                                    backupCount.textContent = parseInt(backupCount.textContent) + 1;
                                }
                                
                                statusMessage.textContent = `${clipboard.file} 备份完成!`;
                                
                                // 重置状态
                                progressBar.style.width = '0%';
                                
                                // 检查是否所有重要文件都已备份
                                const backedUpImportantFiles = backedUpFiles.filter(file => {
                                    const fileItem = Array.from(fileItems).find(item => item.getAttribute('data-file') === file);
                                    return fileItem && fileItem.getAttribute('data-important') === 'true';
                                });
                                
                                if (backedUpImportantFiles.length === importantFilesCount) {
                                    statusMessage.textContent = '所有重要文件备份完成!';
                                    completeBtn.style.display = 'inline-block';
                                }
                            }
                        }, 50);
                    }
                });
                
                // 点击页面其他地方关闭右键菜单
                document.addEventListener('click', function() {
                    contextMenu.style.display = 'none';
                });
                
                // 创建五彩纸屑效果
                function createConfetti() {
                    const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
                    
                    for (let i = 0; i < 50; i++) {
                        const confetti = document.createElement('div');
                        confetti.classList.add('confetti');
                        confetti.style.left = Math.random() * 100 + 'vw';
                        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                        confetti.style.width = Math.random() * 10 + 5 + 'px';
                        confetti.style.height = Math.random() * 10 + 5 + 'px';
                        confetti.style.animationDuration = Math.random() * 2 + 2 + 's';
                        document.body.appendChild(confetti);
                        
                        // 动画结束后移除元素
                        setTimeout(() => {
                            confetti.remove();
                        }, 3000);
                    }
                }
                
                // 完成按钮点击
                completeBtn.addEventListener('click', function() {
                    // 检查是否备份了所有必须的文件
                    const missingRequiredFiles = [];
                    requiredFiles.forEach(fileName => {
                        if (!backedUpFiles.includes(fileName)) {
                            missingRequiredFiles.push(fileName);
                        }
                    });
                    
                    if (missingRequiredFiles.length > 0) {
                        // 有必须备份的文件未备份
                        alert(`还有一些重要文件没有备份:\n${missingRequiredFiles.join('\n')}\n\n请仔细思考，重新备份这些重要文件!`);
                    } else {
                        // 所有必须的文件都已备份，显示成功界面
                        
                        // 显示成功弹窗和五彩纸屑
                        createConfetti();
                        
                        // 显示成功弹窗
                        const modal = document.getElementById('success-modal');
                        modal.style.display = 'flex';
                        
                        // 标记关卡已完成
                        localStorage.setItem('backup_completed', 'true');
                        localStorage.setItem('level3_completed', 'true');
                        
                        // 3秒后自动返回到index.html
                        setTimeout(() => {
                            window.location.href = '../数据安全小卫士闯关乐园.html';
                        }, 3000);
                    }
                });
            }
        });
        
        // 初始化AI助手
        function initAI() {
            // 引入AI助手脚本
            const aiScript = document.createElement('script');
            aiScript.src = '../ai-assistant.js';
            aiScript.onload = function() {
                const configScript = document.createElement('script');
                configScript.src = '../ai-config.js';
                configScript.onload = function() {
                    // 初始化AI助手
                    const assistant = window.initPageAIAssistant({
                        context: '数据备份操作练习'
                    });
                    
                    window.aiAssistant = assistant;
                    
                    // 视频播放完成时提示和总结
                    const video = document.getElementById('myVideo');
                    if (video) {
                        video.addEventListener('ended', function() {
                            setTimeout(() => {
                                // 提供视频内容总结
                                provideVideoSummary(assistant);
                            }, 1000);
                        });
                        
                        // 监听视频播放进度，在关键时间点提供提示
                        video.addEventListener('timeupdate', function() {
                            const currentTime = this.currentTime;
                            const duration = this.duration;
                            
                            if (duration > 0) {
                                const progress = currentTime / duration;
                                
                                // 在25%进度时提供第一个提示
                                if (progress >= 0.25 && progress < 0.3 && !this.tip25Shown) {
                                    this.tip25Shown = true;
                                    assistant.addMessage('💡 注意视频中提到的重要概念：数据备份的重要性！', 'assistant');
                                }
                                
                                // 在50%进度时提供第二个提示
                                if (progress >= 0.5 && progress < 0.55 && !this.tip50Shown) {
                                    this.tip50Shown = true;
                                    assistant.addMessage('🔍 思考一下：哪些文件是重要的，需要备份？哪些是临时文件？', 'assistant');
                                }
                                
                                // 在75%进度时提供第三个提示
                                if (progress >= 0.75 && progress < 0.8 && !this.tip75Shown) {
                                    this.tip75Shown = true;
                                    assistant.addMessage('📱 备份方法有很多：U盘、移动硬盘、云存储等，选择适合的方式！', 'assistant');
                                }
                            }
                        });
                    }
                    
                    // 视频内容总结函数
                    function provideVideoSummary(assistant) {
                        // 将所有总结内容合并为一个完整的消息
                        const videoSummaryText = `📹 视频内容总结：

🎯 核心观点：
• 数据备份是保护重要信息的关键措施
• 不是所有文件都需要备份，要学会区分
• 定期备份可以避免数据丢失的风险

📂 需要备份的文件：
• 重要的个人文件（照片、文档等）
• 学习资料和作业
• 有纪念价值的内容

🗑️ 不需要备份的文件：
• 系统临时文件
• 缓存文件
• 可重新下载的软件

💾 备份方法：
• U盘备份（便携，容量有限）
• 移动硬盘（容量大，适合大文件）
• 云存储（方便，但需要网络）

🎮 现在开始实践操作吧！记住要选择真正重要的文件进行备份！`;
                        
                        // 一次性发送完整的总结内容
                        assistant.addMessage(videoSummaryText, 'assistant');
                        
                        // 显示通知
                        setTimeout(() => {
                            assistant.showNotification();
                        }, 1000);
                    }
                    
                    // 监听文件选择和备份操作
                    setTimeout(() => {
                        const fileItems = document.querySelectorAll('.file-item');
                        fileItems.forEach(item => {
                            item.addEventListener('contextmenu', function(e) {
                                const fileName = this.getAttribute('data-file');
                                const isImportant = this.getAttribute('data-important') === 'true';
                                
                                setTimeout(() => {
                                    if (fileName === '旅游照片' && isImportant) {
                                        assistant.addMessage('📸 旅游照片很珍贵，一旦丢失就找不回来了，一定要备份！', 'assistant');
                                    } else if (fileName.includes('临时') || fileName.includes('缓存')) {
                                        assistant.addMessage('🗂️ 临时文件和缓存文件可以重新生成，不需要备份哦！', 'assistant');
                                    } else if (fileName.includes('游戏')) {
                                        assistant.addMessage('🎮 游戏程序可以重新下载安装，不是必须备份的文件。', 'assistant');
                                    }
                                }, 800);
                            });
                        });
                        
                        // 监听备份成功
                        const backupArea = document.getElementById('backupFiles');
                        if (backupArea) {
                            const backupObserver = new MutationObserver(function(mutations) {
                                mutations.forEach(function(mutation) {
                                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                        const addedFile = mutation.addedNodes[0];
                                        if (addedFile.classList && addedFile.classList.contains('file-item')) {
                                            const fileName = addedFile.textContent;
                                            setTimeout(() => {
                                                if (fileName.includes('旅游照片')) {
                                                    assistant.addMessage('✅ 太棒了！旅游照片备份成功，现在不用担心丢失了！', 'assistant');
                                                } else {
                                                    assistant.addMessage('✅ 文件备份成功！养成定期备份的好习惯很重要！', 'assistant');
                                                }
                                            }, 500);
                                        }
                                    }
                                });
                            });
                            backupObserver.observe(backupArea, { childList: true });
                        }
                    }, 2000);
                    
                    // 完成备份时的祝贺
                    const completeBtn = document.getElementById('completeBtn');
                    if (completeBtn) {
                        completeBtn.addEventListener('click', function() {
                            setTimeout(() => {
                                assistant.addMessage('🎉 太棒了！你学会了备份重要数据！这样就不怕数据丢失了！', 'assistant');
                                assistant.showNotification();
                            }, 1000);
                        });
                    }
                };
                document.head.appendChild(configScript);
            };
            document.head.appendChild(aiScript);
        }
        
        // 页面加载完成后初始化AI助手
        setTimeout(initAI, 500);
    </script>
</body>
</html> 
/**
 * 智能AI评价对话系统
 * 根据学生回答动态生成问题和回复
 */

class IntelligentAIEvaluator {
    constructor(apiConfig) {
        this.apiConfig = apiConfig;
        this.conversationContext = {
            studentProfile: {
                completedLevels: 0,
                strengths: [],
                weaknesses: [],
                interactionStyle: 'unknown',
                responseQuality: []
            },
            currentTopic: null,
            questionsAsked: [],
            studentResponses: [],
            evaluationProgress: {
                hazardIdentification: { covered: false, score: 0 },
                safeSharing: { covered: false, score: 0 },
                dataBackup: { covered: false, score: 0 },
                protectionMeasures: { covered: false, score: 0 }
            }
        };
        
        this.questionPool = this.initializeQuestionPool();
        this.responseAnalyzer = new ResponseAnalyzer();
    }
    
    // 初始化问题库
    initializeQuestionPool() {
        return {
            // 开场问题
            opening: [
                "你好！我是AI评价老师。恭喜你完成了数据安全学习！能先告诉我，你觉得这次学习最有收获的是什么吗？",
                "欢迎来到AI评价环节！看到你完成了所有关卡，真的很棒！先聊聊，哪个关卡让你印象最深刻？",
                "你好！我是你的AI学习伙伴。通过这次数据安全学习，你有什么新的认识吗？"
            ],
            
            // 数据隐患识别相关问题
            hazardIdentification: {
                basic: [
                    "你能说说什么是数据隐患吗？",
                    "在日常生活中，你觉得哪些行为可能会泄露个人信息？",
                    "陌生人加好友这件事，你怎么看？"
                ],
                scenario: [
                    "假如你收到一条短信，说点击链接就能领取免费话费，你会怎么做？为什么？",
                    "你的同学让你扫一个二维码说可以免费下载游戏，但你不知道这个二维码的来源，你会扫吗？",
                    "如果有人在网上问你的生日、家庭住址这些信息，你会告诉他吗？"
                ],
                application: [
                    "根据你的学习，你觉得我们应该如何识别网络上的安全隐患？",
                    "你现在会教你的朋友或家人如何避免数据安全隐患吗？怎么教？"
                ]
            },
            
            // 安全分享相关问题
            safeSharing: {
                basic: [
                    "你平时会在朋友圈或社交媒体上分享什么内容？",
                    "你觉得发朋友圈时需要注意什么？",
                    "什么样的照片或信息是不应该在网上分享的？"
                ],
                scenario: [
                    "你要发一张在学校门口的合影到朋友圈，你会注意什么？",
                    "假如你去旅游，想要分享旅行照片，你会怎么保护自己的隐私？",
                    "如果你的朋友在朋友圈晒出了自己的身份证照片，你会提醒他吗？怎么提醒？"
                ],
                application: [
                    "现在你对网络分享的安全意识有什么变化？",
                    "你会如何设置社交媒体的隐私权限？"
                ]
            },
            
            // 数据备份相关问题
            dataBackup: {
                basic: [
                    "你觉得为什么要备份数据？",
                    "哪些文件你觉得是重要的，需要备份？",
                    "你知道有哪些备份方法吗？"
                ],
                scenario: [
                    "假如你的电脑突然坏了，哪些文件的丢失会让你最难过？",
                    "你的手机里有很多重要照片，你会怎么保护它们？",
                    "如果你要保存重要的学习资料，你会选择什么方式？"
                ],
                application: [
                    "学习了数据备份后，你打算如何管理自己的重要文件？",
                    "你会定期备份数据吗？多久备份一次比较合适？"
                ]
            },
            
            // 保护措施相关问题
            protectionMeasures: {
                basic: [
                    "你知道有哪些保护数据的方法吗？",
                    "在不同的情况下，我们需要采取什么样的保护措施？",
                    "你觉得密码重要吗？为什么？"
                ],
                scenario: [
                    "在公共场所使用WiFi时，你会注意什么？",
                    "如果你要在网上填写个人信息，你会怎么保护自己？",
                    "你需要把重要文件发给老师，你会选择什么方式？"
                ],
                application: [
                    "现在你知道了这么多保护措施，你觉得哪些是你日常生活中能用到的？",
                    "你会把这些保护方法教给你的家人朋友吗？"
                ]
            },
            
            // 跟进问题模板
            followUp: {
                encouragement: [
                    "说得很好！能再详细说说吗？",
                    "这个想法不错！你还有其他的例子吗？",
                    "很有道理！那你觉得还有什么需要注意的吗？"
                ],
                clarification: [
                    "能举个具体的例子说明吗？",
                    "这个情况下你会怎么做呢？",
                    "你为什么这么认为？"
                ],
                deepening: [
                    "如果遇到这种情况，你会怎么处理？",
                    "你觉得这样做的原因是什么？",
                    "这让你想到了什么？"
                ]
            }
        };
    }
    
    // 开始对话
    async startConversation(studentData) {
        this.conversationContext.studentProfile.completedLevels = studentData.completedLevels || 0;
        
        // 选择开场问题
        const openingQuestions = this.questionPool.opening;
        const selectedOpening = openingQuestions[Math.floor(Math.random() * openingQuestions.length)];
        
        this.conversationContext.questionsAsked.push({
            question: selectedOpening,
            type: 'opening',
            timestamp: Date.now()
        });
        
        return selectedOpening;
    }
    
    // 处理学生回答并生成下一个问题
    async processResponse(userResponse) {
        try {
            // 分析学生回答
            const analysis = await this.analyzeResponse(userResponse);
            
            // 更新学生档案
            this.updateStudentProfile(analysis);
            
            // 生成回复和下一个问题
            const aiResponse = await this.generateIntelligentResponse(userResponse, analysis);
            
            return aiResponse;
            
        } catch (error) {
            console.error('处理学生回答失败:', error);
            return this.generateFallbackResponse(userResponse);
        }
    }
    
    // 智能分析学生回答
    async analyzeResponse(userResponse) {
        const lastQuestion = this.conversationContext.questionsAsked[this.conversationContext.questionsAsked.length - 1];
        
        // 构建分析提示词
        const analysisPrompt = `
你是一位专业的小学教育评价专家。请分析以下四年级学生对数据安全问题的回答：

问题：${lastQuestion.question}
学生回答：${userResponse}

请从以下方面分析：
1. 理解程度（1-5分）：学生是否理解了问题的核心
2. 知识掌握（1-5分）：回答中体现的数据安全知识掌握程度
3. 应用能力（1-5分）：是否能将知识应用到实际场景
4. 表达清晰度（1-5分）：回答是否清晰、逻辑性强
5. 主要优点：回答中的亮点
6. 需要改进：可以提升的地方
7. 建议跟进：应该继续深入的方向

请以JSON格式返回分析结果：
{
  "understanding": 分数,
  "knowledge": 分数,
  "application": 分数,
  "expression": 分数,
  "strengths": ["优点1", "优点2"],
  "improvements": ["改进点1", "改进点2"],
  "followUp": "建议跟进方向"
}`;

        if (this.apiConfig.apiKey) {
            try {
                const response = await fetch(this.apiConfig.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiConfig.apiKey}`
                    },
                    body: JSON.stringify({
                        model: this.apiConfig.model,
                        messages: [
                            { role: 'system', content: analysisPrompt },
                            { role: 'user', content: userResponse }
                        ],
                        temperature: 0.7,
                        max_tokens: 500
                    })
                });
                
                const data = await response.json();
                const aiAnalysis = data.choices[0].message.content;
                
                try {
                    return JSON.parse(aiAnalysis);
                } catch (e) {
                    console.warn('AI分析结果解析失败，使用本地分析');
                    return this.localAnalyzeResponse(userResponse);
                }
                
            } catch (error) {
                console.error('AI分析请求失败:', error);
                return this.localAnalyzeResponse(userResponse);
            }
        } else {
            return this.localAnalyzeResponse(userResponse);
        }
    }
    
    // 本地分析回答（备用方案）
    localAnalyzeResponse(userResponse) {
        const length = userResponse.length;
        const hasExamples = /例如|比如|举例|比方说/.test(userResponse);
        const hasReasoning = /因为|所以|因此|由于/.test(userResponse);
        const hasSecurityTerms = /安全|隐私|保护|备份|密码|风险|危险/.test(userResponse);
        const hasPersonalThoughts = /我觉得|我认为|我想|我的看法/.test(userResponse);
        
        let understanding = 2;
        let knowledge = 2;
        let application = 2;
        let expression = 2;
        
        // 根据回答特征调整分数
        if (length > 20) expression += 1;
        if (length > 50) expression += 1;
        if (hasExamples) application += 2;
        if (hasReasoning) understanding += 2;
        if (hasSecurityTerms) knowledge += 2;
        if (hasPersonalThoughts) expression += 1;
        
        // 确保分数在1-5范围内
        understanding = Math.min(Math.max(understanding, 1), 5);
        knowledge = Math.min(Math.max(knowledge, 1), 5);
        application = Math.min(Math.max(application, 1), 5);
        expression = Math.min(Math.max(expression, 1), 5);
        
        const strengths = [];
        const improvements = [];
        
        if (hasExamples) strengths.push("能够举出具体例子说明");
        if (hasReasoning) strengths.push("思路清晰，有逻辑性");
        if (hasSecurityTerms) strengths.push("掌握了相关安全概念");
        if (hasPersonalThoughts) strengths.push("有自己的思考和观点");
        
        if (length < 10) improvements.push("回答可以更详细一些");
        if (!hasExamples) improvements.push("可以尝试举一些具体例子");
        if (!hasSecurityTerms) improvements.push("可以运用更多学到的安全知识");
        
        if (strengths.length === 0) strengths.push("积极参与回答问题");
        if (improvements.length === 0) improvements.push("继续保持学习的积极性");
        
        return {
            understanding,
            knowledge,
            application,
            expression,
            strengths,
            improvements,
            followUp: "可以进一步探讨实际应用场景"
        };
    }
    
    // 更新学生档案
    updateStudentProfile(analysis) {
        const profile = this.conversationContext.studentProfile;
        
        // 记录回答质量
        profile.responseQuality.push({
            understanding: analysis.understanding,
            knowledge: analysis.knowledge,
            application: analysis.application,
            expression: analysis.expression,
            timestamp: Date.now()
        });
        
        // 更新优势和弱点
        analysis.strengths.forEach(strength => {
            if (!profile.strengths.includes(strength)) {
                profile.strengths.push(strength);
            }
        });
        
        analysis.improvements.forEach(improvement => {
            if (!profile.weaknesses.includes(improvement)) {
                profile.weaknesses.push(improvement);
            }
        });
        
        // 分析互动风格
        if (profile.responseQuality.length >= 2) {
            const avgLength = this.conversationContext.studentResponses.reduce((sum, resp) => sum + resp.length, 0) / this.conversationContext.studentResponses.length;
            const avgQuality = profile.responseQuality.reduce((sum, q) => sum + (q.understanding + q.knowledge + q.application + q.expression), 0) / (profile.responseQuality.length * 4);
            
            if (avgLength > 50 && avgQuality > 3.5) {
                profile.interactionStyle = 'detailed';
            } else if (avgLength > 20 && avgQuality > 3) {
                profile.interactionStyle = 'balanced';
            } else {
                profile.interactionStyle = 'concise';
            }
        }
    }
    
    // 生成智能回复
    async generateIntelligentResponse(userResponse, analysis) {
        // 保存学生回答
        this.conversationContext.studentResponses.push(userResponse);
        
        // 生成针对性反馈
        const feedback = this.generatePersonalizedFeedback(analysis);
        
        // 决定下一个问题
        const nextQuestion = this.selectNextQuestion(analysis);
        
        let response = feedback;
        
        if (nextQuestion) {
            response += `\n\n${nextQuestion}`;
            
            // 记录问题
            this.conversationContext.questionsAsked.push({
                question: nextQuestion,
                type: this.identifyQuestionType(nextQuestion),
                timestamp: Date.now()
            });
        } else {
            // 对话结束
            response += "\n\n" + this.generateFinalEvaluation();
        }
        
        return response;
    }
    
    // 生成个性化反馈
    generatePersonalizedFeedback(analysis) {
        const encouragements = [
            "很棒的回答！", "说得很好！", "你的想法很有道理！", 
            "这个观点很不错！", "我能看出你认真思考了！"
        ];
        
        const specificFeedback = [];
        
        // 根据分析结果生成具体反馈
        if (analysis.understanding >= 4) {
            specificFeedback.push("你很好地理解了问题的核心");
        }
        
        if (analysis.knowledge >= 4) {
            specificFeedback.push("能看出你掌握了相关的安全知识");
        }
        
        if (analysis.application >= 4) {
            specificFeedback.push("你能很好地将知识应用到实际情况中");
        }
        
        if (analysis.expression >= 4) {
            specificFeedback.push("你的表达很清晰");
        }
        
        // 选择鼓励语
        const encouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
        
        // 组合反馈
        let feedback = encouragement;
        
        if (specificFeedback.length > 0) {
            feedback += specificFeedback.join("，") + "。";
        }
        
        // 添加优点
        if (analysis.strengths.length > 0) {
            const strength = analysis.strengths[Math.floor(Math.random() * analysis.strengths.length)];
            feedback += `特别是${strength}这一点很好。`;
        }
        
        return feedback;
    }
    
    // 选择下一个问题
    selectNextQuestion(analysis) {
        const questionsAsked = this.conversationContext.questionsAsked.length;
        
        // 如果已经问了5个问题，结束对话
        if (questionsAsked >= 5) {
            return null;
        }
        
        // 根据学生表现选择问题领域
        const uncoveredTopics = this.getUncoveredTopics();
        
        if (uncoveredTopics.length === 0) {
            // 所有主题都覆盖了，问一些综合性问题
            return this.selectSummaryQuestion();
        }
        
        // 选择一个未覆盖的主题
        const selectedTopic = uncoveredTopics[Math.floor(Math.random() * uncoveredTopics.length)];
        
        // 根据学生的互动风格选择问题类型
        const questionType = this.selectQuestionType(analysis);
        
        return this.selectQuestionFromPool(selectedTopic, questionType);
    }
    
    // 获取未覆盖的主题
    getUncoveredTopics() {
        const topics = ['hazardIdentification', 'safeSharing', 'dataBackup', 'protectionMeasures'];
        return topics.filter(topic => !this.conversationContext.evaluationProgress[topic].covered);
    }
    
    // 选择问题类型
    selectQuestionType(analysis) {
        const profile = this.conversationContext.studentProfile;
        
        if (profile.interactionStyle === 'detailed') {
            return 'scenario'; // 详细回答者适合情景题
        } else if (profile.interactionStyle === 'concise') {
            return 'basic'; // 简洁回答者适合基础题
        } else {
            return Math.random() > 0.5 ? 'scenario' : 'application';
        }
    }
    
    // 从问题池中选择问题
    selectQuestionFromPool(topic, questionType) {
        const topicQuestions = this.questionPool[topic];
        
        if (!topicQuestions || !topicQuestions[questionType]) {
            // 备用选择
            const allQuestions = Object.values(topicQuestions || {}).flat();
            return allQuestions[Math.floor(Math.random() * allQuestions.length)];
        }
        
        const questions = topicQuestions[questionType];
        const selectedQuestion = questions[Math.floor(Math.random() * questions.length)];
        
        // 标记主题已覆盖
        this.conversationContext.evaluationProgress[topic].covered = true;
        
        return selectedQuestion;
    }
    
    // 选择总结性问题
    selectSummaryQuestion() {
        const summaryQuestions = [
            "通过这次学习，你觉得自己在数据安全方面最大的收获是什么？",
            "现在你会如何向其他同学介绍数据安全的重要性？",
            "学了这些知识后，你在日常生活中会有什么改变？"
        ];
        
        return summaryQuestions[Math.floor(Math.random() * summaryQuestions.length)];
    }
    
    // 识别问题类型
    identifyQuestionType(question) {
        if (question.includes('如果') || question.includes('假如')) return 'scenario';
        if (question.includes('为什么') || question.includes('怎么')) return 'understanding';
        if (question.includes('会') || question.includes('能')) return 'application';
        return 'open';
    }
    
    // 生成备用回复
    generateFallbackResponse(userResponse) {
        const fallbackResponses = [
            "谢谢你的回答！我能感受到你对这个问题的思考。",
            "很好！你的想法让我看到了你的学习成果。",
            "不错的回答！看得出你在认真学习数据安全知识。"
        ];
        
        const response = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
        
        // 简单的下一个问题
        const simpleQuestions = [
            "那你觉得在日常生活中，还有哪些地方需要注意数据安全？",
            "你会把学到的安全知识分享给家人朋友吗？",
            "这次学习让你对网络安全有什么新的认识？"
        ];
        
        if (this.conversationContext.questionsAsked.length < 4) {
            const nextQuestion = simpleQuestions[Math.floor(Math.random() * simpleQuestions.length)];
            return `${response}\n\n${nextQuestion}`;
        } else {
            return response + "\n\n" + this.generateFinalEvaluation();
        }
    }
    
    // 生成最终评价
    generateFinalEvaluation() {
        const profile = this.conversationContext.studentProfile;
        const avgScores = this.calculateAverageScores();
        
        let evaluation = "非常棒！通过我们的对话，我对你的学习情况有了很好的了解。\n\n";
        
        // 根据平均分数给出评价
        if (avgScores.overall >= 4) {
            evaluation += "🌟 你的表现非常优秀！不仅掌握了数据安全的核心概念，还能灵活运用到实际场景中。";
        } else if (avgScores.overall >= 3) {
            evaluation += "👍 你的表现很好！对数据安全有了良好的理解，继续保持这种学习态度。";
        } else {
            evaluation += "💪 你已经有了一个好的开始！继续学习和实践，你会掌握得更好的。";
        }
        
        // 添加个性化建议
        if (profile.strengths.length > 0) {
            evaluation += `\n\n你的优势：${profile.strengths.slice(0, 2).join('、')}。`;
        }
        
        evaluation += "\n\n现在你可以查看其他标签页了解详细的学习数据分析和个性化报告。继续保持学习的热情，你一定能成为真正的数据安全小专家！";
        
        return evaluation;
    }
    
    // 计算平均分数
    calculateAverageScores() {
        const qualities = this.conversationContext.studentProfile.responseQuality;
        
        if (qualities.length === 0) {
            return { overall: 3, understanding: 3, knowledge: 3, application: 3, expression: 3 };
        }
        
        const totals = qualities.reduce((sum, q) => ({
            understanding: sum.understanding + q.understanding,
            knowledge: sum.knowledge + q.knowledge,
            application: sum.application + q.application,
            expression: sum.expression + q.expression
        }), { understanding: 0, knowledge: 0, application: 0, expression: 0 });
        
        const count = qualities.length;
        const averages = {
            understanding: totals.understanding / count,
            knowledge: totals.knowledge / count,
            application: totals.application / count,
            expression: totals.expression / count
        };
        
        averages.overall = (averages.understanding + averages.knowledge + averages.application + averages.expression) / 4;
        
        return averages;
    }
    
    // 获取对话总结
    getConversationSummary() {
        return {
            questionsAsked: this.conversationContext.questionsAsked.length,
            averageScores: this.calculateAverageScores(),
            studentProfile: this.conversationContext.studentProfile,
            topicsCovered: Object.keys(this.conversationContext.evaluationProgress).filter(
                topic => this.conversationContext.evaluationProgress[topic].covered
            )
        };
    }
}

// 响应分析器辅助类
class ResponseAnalyzer {
    constructor() {
        this.securityKeywords = {
            hazardIdentification: ['隐患', '危险', '风险', '陌生人', '链接', '二维码', '诈骗'],
            safeSharing: ['朋友圈', '分享', '隐私', '位置', '照片', '个人信息'],
            dataBackup: ['备份', '保存', '存储', '丢失', '重要', '文件'],
            protectionMeasures: ['保护', '措施', '密码', 'WiFi', '安全', '防护']
        };
    }
    
    identifyTopics(response) {
        const topics = [];
        const lowerResponse = response.toLowerCase();
        
        for (const [topic, keywords] of Object.entries(this.securityKeywords)) {
            if (keywords.some(keyword => lowerResponse.includes(keyword))) {
                topics.push(topic);
            }
        }
        
        return topics;
    }
}

// 导出
if (typeof window !== 'undefined') {
    window.IntelligentAIEvaluator = IntelligentAIEvaluator;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = IntelligentAIEvaluator;
}

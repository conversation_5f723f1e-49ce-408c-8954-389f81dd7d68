# 数据安全小卫士 AI助手使用说明

## 📋 功能概述

AI助手是一个智能学习伙伴，集成在每个学习页面中，可以：
- 回答学习相关的问题
- 提供实时学习指导
- 根据学习进度给出鼓励和建议
- 解答数据安全相关疑问

## 🛠️ 配置步骤

### 1. 获取GLM4.5 API密钥

1. 访问智谱AI开放平台：https://open.bigmodel.cn/
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 复制生成的API密钥

### 2. 配置API密钥

打开 `ai-config.js` 文件，找到以下代码：

```javascript
const API_CONFIG = {
    apiKey: '', // 请在这里填写您的GLM4.5 API密钥
    apiUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'glm-4-plus'
};
```

将您的API密钥填入 `apiKey` 字段：

```javascript
const API_CONFIG = {
    apiKey: 'your-api-key-here', // 替换为您的真实API密钥
    apiUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'glm-4-plus'
};
```

## 🎯 使用方法

### 启动AI助手

1. 打开任意学习页面
2. 在页面右下角（主页面在左下角）会看到一个带有表情符号的圆形按钮
3. 点击按钮即可打开AI助手对话窗口

### 与AI助手交互

1. **直接提问**：在输入框中输入问题，点击"发送"或按回车键
2. **快速问题**：点击对话窗口底部的预设问题按钮
3. **关闭对话**：点击对话窗口右上角的"×"按钮

### AI助手特性

- **个性化回答**：根据当前学习页面提供针对性回答
- **简单易懂**：使用适合小学四年级学生的语言
- **实时指导**：在学习过程中主动提供提示和鼓励
- **记忆对话**：保持对话上下文，支持连续提问

## 🎨 不同页面的AI助手

### 主页面 - 小卫士 🛡️
- 介绍整体学习内容
- 回答关于数据安全的基础问题
- 提供学习建议

### 数据隐患辨一辨 - 安全小卫士 🔍
- 帮助识别数据安全隐患
- 解释安全行为和危险行为的区别
- 提供分类游戏的指导

### 数据分享试一试 - 分享小助手 📱
- 指导安全的社交媒体使用
- 解释隐私保护的重要性
- 提供朋友圈分享建议

### 数据备份存一存 - 备份小管家 💾
- 解释数据备份的重要性
- 指导如何选择重要文件
- 介绍备份方法和技巧

### 保护措施选一选 - 保护小专家 🛡️
- 介绍各种数据保护措施
- 帮助理解不同场景的保护需求
- 指导正确的保护措施选择

## ❓ 常见问题

### Q: AI助手没有出现怎么办？
A: 请检查：
1. 是否正确配置了API密钥
2. 网络连接是否正常
3. 浏览器是否支持JavaScript
4. 是否有广告拦截插件阻止了脚本

### Q: AI助手回答很慢或没有回应？
A: 可能原因：
1. 网络连接不稳定
2. API服务繁忙
3. API密钥配额用完
4. 请求格式有误

### Q: 如何自定义AI助手的回答风格？
A: 可以在 `ai-config.js` 文件中修改各页面的配置，调整：
- `name`: 助手名称
- `avatar`: 助手头像表情
- `context`: 学习环境描述
- `quickQuestions`: 快速问题列表

### Q: 如何更换API服务提供商？
A: 修改 `ai-config.js` 中的 `API_CONFIG`：
- `apiUrl`: 更换为新的API地址
- `model`: 更换为对应的模型名称
- 调整请求格式（如需要）

## 🔧 技术说明

### 文件结构
```
├── ai-assistant.js        # AI助手核心功能
├── ai-config.js          # 配置文件
├── AI助手使用说明.md      # 使用说明（本文件）
├── 数据安全小卫士闯关乐园.html  # 主页面
└── link/                 # 子页面目录
    ├── 数据隐患辨一辨.html
    ├── 数据分享试一试.html
    ├── 数据备份存一存.html
    └── 保护措施选一选.html
```

### API调用格式
```javascript
{
    model: 'glm-4-plus',
    messages: [
        { role: 'system', content: '系统提示词' },
        { role: 'user', content: '用户问题' }
    ],
    temperature: 0.7,
    max_tokens: 500
}
```

### 自定义配置示例
```javascript
// 在页面中自定义AI助手
const customAssistant = window.initPageAIAssistant({
    name: '自定义助手',
    avatar: '🤖',
    context: '自定义学习环境',
    position: 'top-right',
    apiKey: 'your-custom-api-key'
});
```

## 📞 技术支持

如果遇到技术问题，请检查：
1. 浏览器控制台是否有错误信息
2. 网络请求是否成功
3. API密钥是否正确配置
4. 文件路径是否正确

## 🔄 更新日志

### v1.0.0
- 初始版本发布
- 支持GLM4.5 API
- 集成到所有学习页面
- 提供个性化学习指导

---

💡 **提示**：AI助手是学习的好伙伴，但不能替代自主思考。鼓励学生在使用AI助手的同时，培养独立思考和解决问题的能力。


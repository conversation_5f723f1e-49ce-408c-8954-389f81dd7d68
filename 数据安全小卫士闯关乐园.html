<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护好自己的数据</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
            background-attachment: fixed;
            overflow-x: hidden;
        }
        .container {
            width: 80%;
            max-width: 800px;
            margin: 20px auto;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 10px;
            position: relative;
            font-weight: normal;
            margin-top: 30px;
        }
        .big-title {
            font-size: 3.2rem;
            margin-bottom: 15px;
            padding: 20px;
            width: 100%;
            text-align: center;
            background: linear-gradient(to right, #ff7e5f, #feb47b, #ffeb3b, #7bed9f, #70a1ff, #9b59b6);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            font-weight: bold;
            animation: titleColorChange 6s infinite;
            letter-spacing: 1px;
        }
        
        @keyframes titleColorChange {
            0% { filter: hue-rotate(0deg); transform: translateY(0); }
            25% { transform: translateY(-5px); }
            50% { filter: hue-rotate(180deg); transform: translateY(0); }
            75% { transform: translateY(-5px); }
            100% { filter: hue-rotate(360deg); transform: translateY(0); }
        }
        .link-box {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.5s ease;
            cursor: default;
            text-decoration: none;
            color: #333;
            display: block;
            position: relative;
            opacity: 1;
        }
        .link-box.active {
            cursor: pointer;
        }
        .link-box.active:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .link-box.locked {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: transparent;
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.15);
            cursor: not-allowed;
        }
        .link-box.locked .level-indicator {
            opacity: 0.3;
        }
        .link-box.locked h2 {
            color: transparent;
            text-shadow: none;
        }
        .link-box h2 {
            margin: 0;
            font-size: 1.5rem;
            color: #3498db;
            transition: all 0.5s ease;
        }
        .level-indicator {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.5s ease;
        }
        .shield-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            animation: shieldFloat 2s ease-in-out infinite;
            filter: drop-shadow(0 0 5px rgba(52, 152, 219, 0.5));
        }
        @keyframes shieldFloat {
            0% {
                transform: translateY(-50%) translateX(0) rotate(0deg);
            }
            50% {
                transform: translateY(-50%) translateX(5px) rotate(5deg);
            }
            100% {
                transform: translateY(-50%) translateX(0) rotate(0deg);
            }
        }
        .completed-text {
            position: absolute;
            right: 70px;
            top: 50%;
            transform: translateY(-50%);
            color: #27ae60;
            font-weight: bold;
        }
        
        /* 大盾牌特效样式 */
        .shield-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 30, 60, 0.95));
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.8s ease;
            perspective: 1000px;
        }
        .shield-overlay.active {
            display: flex;
            opacity: 1;
        }
        .big-shield-container {
            position: relative;
            width: 300px;
            height: 300px;
            animation: containerFloat 5s ease-in-out infinite;
            transform-style: preserve-3d;
        }
        @keyframes containerFloat {
            0% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0); }
        }
        .big-shield {
            width: 100%;
            height: 100%;
            animation: shieldAppear 1s ease-out forwards;
            opacity: 0;
            transform: scale(0.5) rotateY(-30deg);
            filter: drop-shadow(0 0 20px rgba(52, 152, 219, 0.8));
        }
        @keyframes shieldAppear {
            0% {
                opacity: 0;
                transform: scale(0.5);
            }
            70% {
                opacity: 1;
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        .shield-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(52, 152, 219, 0.6) 0%, rgba(52, 152, 219, 0) 70%);
            border-radius: 50%;
            animation: glowPulse 3s ease-in-out infinite;
            z-index: -1;
        }
        @keyframes glowPulse {
            0% { opacity: 0.5; transform: scale(0.8); }
            50% { opacity: 0.8; transform: scale(1.2); }
            100% { opacity: 0.5; transform: scale(0.8); }
        }
        .shield-text {
            position: absolute;
            bottom: 20%;
            left: 0;
            right: 0;
            text-align: center;
            color: white;
            font-size: 2.5rem;
            font-weight: bold;
            opacity: 0;
            transform: translateY(30px);
            animation: textAppear 1s ease-out forwards 1s;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        @keyframes textAppear {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .confetti {
            position: absolute;
            z-index: 1001;
            animation: confettiFall linear infinite;
        }
        @keyframes confettiFall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
        .star {
            position: absolute;
            background-color: white;
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
            animation: starTwinkle linear infinite;
        }
        @keyframes starTwinkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }
        .continue-btn {
            position: absolute;
            bottom: 10%;
            padding: 15px 40px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            transform: translateY(20px);
            animation: textAppear 1s ease-out forwards 2s;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.5);
        }
        .continue-btn:hover {
            background: linear-gradient(135deg, #2980b9, #3498db);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.7);
        }
        .continue-btn:active {
            transform: translateY(-1px);
        }
        
        /* 关卡出现动画 */
        @keyframes levelAppear {
            0% {
                opacity: 0;
                transform: translateX(-50px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* 关卡解锁动画 */
        @keyframes levelUnlock {
            0% {
                background-color: #f5f5f5;
                color: transparent;
            }
            50% {
                background-color: #e3f2fd;
                box-shadow: 0 0 30px rgba(52, 152, 219, 0.8);
            }
            100% {
                background-color: white;
                color: #333;
            }
        }
        
        /* 重置按钮样式 */
        .reset-btn {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .reset-btn:hover {
            background-color: #c0392b;
            transform: translateY(-3px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }
        .reset-btn:active {
            transform: translateY(-1px);
        }
        
        /* 彩带效果 */
        .ribbon {
            position: absolute;
            width: 4px;
            height: 80px;
            background: linear-gradient(to bottom, transparent, currentColor, transparent);
            transform-origin: 50% 0;
            z-index: 1001;
        }
        
        /* 闪光效果 */
        .sparkle {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: white;
            box-shadow: 0 0 10px 2px white;
            opacity: 0;
            z-index: 1002;
        }
        @keyframes sparkleAnimate {
            0% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1); opacity: 1; }
            100% { transform: scale(0); opacity: 0; }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <h1>小学信息科技四年级（下册） 保护好自己的数据</h1>
    <div class="big-title">欢迎来到数据安全小卫士闯关乐园</div>
    <div class="container">
        <a href="link/数据隐患辨一辨.html" class="link-box active" id="level1" data-level="1">
            <div class="level-indicator">1</div>
            <h2>闯关一：数据隐患辨一辨</h2>
        </a>
        
        <a href="link/数据分享试一试.html" class="link-box locked" id="level2" data-level="2">
            <div class="level-indicator">2</div>
            <h2>闯关二：数据分享试一试</h2>
        </a>
        
        <a href="link/数据备份存一存.html" class="link-box locked" id="level3" data-level="3">
            <div class="level-indicator">3</div>
            <h2>闯关三：数据备份存一存</h2>
        </a>
        
        <a href="link/保护措施选一选.html" class="link-box locked" id="level4" data-level="4">
            <h2>考考你：保护措施选一选</h2>
        </a>
        
        <a href="AI智能评价系统.html" class="link-box locked" id="level5" data-level="5">
            <h2>🤖 AI智能学习评价</h2>
        </a>
        
        <button class="reset-btn" onclick="resetProgress()">重置进度</button>
    </div>

    <!-- 大盾牌特效 -->
    <div class="shield-overlay" id="shieldOverlay">
        <div class="big-shield-container">
            <div class="shield-glow"></div>
            <img src="素材/护盾.png" class="big-shield" alt="护盾">
        </div>
        <div class="shield-text">恭喜你完成了所有关卡！</div>
        <button class="continue-btn" id="continueBtn">继续</button>
    </div>

    <script>
        // 当前解锁关卡 (1-5)
        let currentLevel = 1;
        
        // 页面加载时初始化
        window.onload = function() {
            console.log("页面加载完成");
            
            // 从本地存储加载当前关卡
            loadCurrentLevel();
            
            // 检查是否有已完成的关卡标记
            checkCompletedLevels();
            
            // 更新页面显示
            updateUI();
            
            // 添加继续按钮事件
            document.getElementById('continueBtn').addEventListener('click', function() {
                document.getElementById('shieldOverlay').classList.remove('active');
            });
        };
        
        // 加载当前关卡
        function loadCurrentLevel() {
            const savedLevel = localStorage.getItem('currentLevel');
            if (savedLevel) {
                currentLevel = parseInt(savedLevel);
                console.log("加载关卡进度: " + currentLevel);
            } else {
                currentLevel = 1; // 默认从第一关开始
                saveCurrentLevel();
                console.log("初始化关卡进度: 1");
            }
        }
        
        // 保存当前关卡
        function saveCurrentLevel() {
            localStorage.setItem('currentLevel', currentLevel);
            console.log("保存关卡进度: " + currentLevel);
        }
        
        // 检查是否有已完成的关卡标记
        function checkCompletedLevels() {
            // 检查关卡1是否完成
            if (localStorage.getItem('level1_completed') === 'true') {
                console.log("检测到关卡1已完成");
                if (currentLevel <= 1) {
                    currentLevel = 2;
                    saveCurrentLevel();
                    showNewLevel(2);
                }
                localStorage.removeItem('level1_completed');
            }
            
            // 检查关卡2是否完成
            if (localStorage.getItem('level2_completed') === 'true') {
                console.log("检测到关卡2已完成");
                if (currentLevel <= 2) {
                    currentLevel = 3;
                    saveCurrentLevel();
                    showNewLevel(3);
                }
                localStorage.removeItem('level2_completed');
            }
            
            // 检查关卡3是否完成
            if (localStorage.getItem('level3_completed') === 'true') {
                console.log("检测到关卡3已完成");
                if (currentLevel <= 3) {
                    currentLevel = 4;
                    saveCurrentLevel();
                    showNewLevel(4);
                    
                    // 显示大盾牌特效
                    setTimeout(() => {
                        showBigShield();
                    }, 500);
                }
                localStorage.removeItem('level3_completed');
            }
            
            // 检查关卡4是否完成
            if (localStorage.getItem('level4_completed') === 'true') {
                console.log("检测到关卡4已完成");
                if (currentLevel <= 4) {
                    currentLevel = 5;
                    saveCurrentLevel();
                    showNewLevel(5);
                }
                localStorage.removeItem('level4_completed');
            }
        }
        
        // 更新界面显示
        function updateUI() {
            console.log("更新界面显示，当前关卡: " + currentLevel);
            
            // 处理所有关卡
            for (let i = 1; i <= 5; i++) {
                const levelElement = document.getElementById('level' + i);
                if (!levelElement) continue;
                
                // 移除所有特殊类
                levelElement.classList.remove('active', 'locked');
                
                // 移除所有护盾图标和完成文本
                const existingShield = levelElement.querySelector('.shield-icon');
                if (existingShield) existingShield.remove();
                
                const existingText = levelElement.querySelector('.completed-text');
                if (existingText) existingText.remove();
                
                if (i < currentLevel) {
                    // 已完成的关卡
                    levelElement.classList.add('active');
                    
                    // 对所有已完成关卡都添加"已完成"文本
                    const completed = document.createElement('span');
                    completed.className = 'completed-text';
                    completed.textContent = '已完成';
                    levelElement.appendChild(completed);
                    
                    // 只对前四关添加护盾图标
                    if (i <= 4) {
                        // 添加护盾图标
                        const shield = document.createElement('img');
                        shield.src = '素材/护盾.png';
                        shield.className = 'shield-icon';
                        shield.alt = '已完成';
                        levelElement.appendChild(shield);
                    }
                    
                } else if (i === currentLevel) {
                    // 当前关卡
                    levelElement.classList.add('active');
                    
                } else {
                    // 未解锁关卡
                    levelElement.classList.add('locked');
                }
                
                // 为所有关卡添加点击事件监听器
                levelElement.onclick = handleLevelClick;
            }
        }
        
        // 处理关卡点击事件
        function handleLevelClick(event) {
            // 检查关卡是否锁定
            if (this.classList.contains('locked')) {
                event.preventDefault(); // 阻止默认导航行为
                
                // 添加一个抖动动画效果
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'shake 0.5s ease';
                }, 10);
                
                // 显示提示信息
                alert('请先完成前面的关卡！');
                
                return false;
            }
            
            // 如果关卡未锁定，允许正常导航
            return true;
        }
        
        // 显示新解锁的关卡
        function showNewLevel(level) {
            const levelElement = document.getElementById('level' + level);
            if (levelElement) {
                levelElement.classList.remove('locked');
                levelElement.classList.add('active');
                
                // 添加解锁动画
                levelElement.style.animation = 'levelUnlock 1s ease-out forwards';
                
                // 添加小型庆祝特效
                createLevelUnlockEffect(levelElement);
                
                // 重置动画
                setTimeout(() => {
                    levelElement.style.animation = '';
                }, 1000);
            }
        }
        
        // 创建关卡解锁特效
        function createLevelUnlockEffect(element) {
            // 获取元素位置
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            // 创建闪光效果
            for (let i = 0; i < 10; i++) {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                sparkle.style.left = (centerX - 3 + Math.random() * rect.width) + 'px';
                sparkle.style.top = (centerY - 3 + Math.random() * rect.height) + 'px';
                sparkle.style.animation = `sparkleAnimate ${0.5 + Math.random() * 1}s ease-out forwards`;
                document.body.appendChild(sparkle);
                
                // 动画结束后移除
                setTimeout(() => {
                    sparkle.remove();
                }, 2000);
            }
        }
        
        // 显示大盾牌特效
        function showBigShield() {
            const shieldOverlay = document.getElementById('shieldOverlay');
            shieldOverlay.classList.add('active');
        }
        
        // 重置进度
        function resetProgress() {
            if (confirm('确定要重置所有进度吗？')) {
                currentLevel = 1;
                saveCurrentLevel();
                
                // 清除所有关卡完成标记
                localStorage.removeItem('level1_completed');
                localStorage.removeItem('level2_completed');
                localStorage.removeItem('level3_completed');
                localStorage.removeItem('level4_completed');
                
                // 更新UI
                updateUI();
                
                alert('进度已重置！');
            }
        }
        
        // 初始化AI助手
        function initAI() {
            // 引入AI助手脚本
            const aiScript = document.createElement('script');
            aiScript.src = 'ai-assistant.js';
            aiScript.onload = function() {
                const configScript = document.createElement('script');
                configScript.src = 'ai-config.js';
                configScript.onload = function() {
                    // 初始化AI助手
                    const assistant = window.initPageAIAssistant({
                        context: '数据安全闯关学习主页',
                        position: 'bottom-right' // 主页面放在右侧
                    });
                    
                    window.aiAssistant = assistant;
                    
                    // 监听关卡解锁，提供鼓励
                    const originalShowNewLevel = showNewLevel;
                    if (typeof originalShowNewLevel === 'function') {
                        window.showNewLevel = function(level) {
                            originalShowNewLevel(level);
                            setTimeout(() => {
                                assistant.addMessage(`🎉 恭喜解锁新关卡！继续加油学习数据安全知识吧！`, 'assistant');
                                assistant.showNotification();
                            }, 1500);
                        };
                    }
                    
                    // 欢迎消息
                    setTimeout(() => {
                        assistant.addMessage('🛡️ 欢迎来到数据安全小卫士闯关乐园！我是你的学习伙伴，有任何问题都可以问我哦！', 'assistant');
                    }, 2000);
                };
                document.head.appendChild(configScript);
            };
            document.head.appendChild(aiScript);
        }
        
        // 页面加载完成后初始化AI助手
        setTimeout(initAI, 1000);
    </script>
</body>
</html> 
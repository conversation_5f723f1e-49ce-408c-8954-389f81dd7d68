/**
 * 超精确数据追踪器
 * 专门为AI智能学习助手提供最精确的学习数据分析
 */

class UltraPreciseDataTracker {
    constructor() {
        this.sessionData = {
            startTime: Date.now(),
            endTime: null,
            totalActiveTime: 0,
            effectiveTime: 0,
            pageVisits: {},
            interactions: [],
            aiConversations: [],
            learningProgress: {},
            skillDemonstrations: {
                hazardIdentification: { count: 0, quality: [] },
                safeSharing: { count: 0, quality: [] },
                dataBackup: { count: 0, quality: [] },
                protectionMeasures: { count: 0, quality: [] }
            },
            operationAccuracy: {
                correct: 0,
                incorrect: 0,
                total: 0
            },
            behaviorPatterns: {
                focusEvents: [],
                clickEvents: [],
                scrollEvents: [],
                typingEvents: []
            }
        };
        
        this.isActive = true;
        this.lastActivityTime = Date.now();
        this.focusStartTime = Date.now();
        this.pageVisibilitySupported = typeof document.hidden !== "undefined";
        
        console.log('🎯 超精确数据追踪器已初始化');
    }
    
    // 初始化追踪
    init() {
        this.setupEventListeners();
        this.startSessionTracking();
        this.loadExistingData();
        console.log('📊 开始超精确数据追踪');
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 页面可见性变化
        if (this.pageVisibilitySupported) {
            document.addEventListener('visibilitychange', () => {
                this.handleVisibilityChange();
            });
        }
        
        // 鼠标活动
        document.addEventListener('click', (e) => {
            this.recordInteraction('click', e);
        });
        
        document.addEventListener('mousemove', (e) => {
            this.recordActivity();
        });
        
        // 键盘活动
        document.addEventListener('keydown', (e) => {
            this.recordInteraction('keydown', e);
            this.recordActivity();
        });
        
        // 滚动活动
        document.addEventListener('scroll', () => {
            this.recordInteraction('scroll');
            this.recordActivity();
        });
        
        // 窗口焦点
        window.addEventListener('focus', () => {
            this.handleFocusChange(true);
        });
        
        window.addEventListener('blur', () => {
            this.handleFocusChange(false);
        });
        
        // 页面卸载前保存数据
        window.addEventListener('beforeunload', () => {
            this.saveSessionData();
        });
        
        // 定期保存数据
        setInterval(() => {
            this.saveSessionData();
        }, 30000); // 每30秒保存一次
    }
    
    // 开始会话追踪
    startSessionTracking() {
        // 记录页面访问
        const currentPage = this.getCurrentPageType();
        this.recordPageVisit(currentPage);
        
        // 开始活动时间计算
        this.startActivityTimer();
        
        // 监听学习进度变化
        this.monitorLearningProgress();
        
        // 监听AI对话
        this.monitorAIConversations();
    }
    
    // 获取当前页面类型
    getCurrentPageType() {
        const url = window.location.href;
        const pathname = window.location.pathname;
        
        if (pathname.includes('数据安全小卫士闯关乐园')) return 'main';
        if (pathname.includes('数据隐患辨一辨')) return 'level1';
        if (pathname.includes('数据分享试一试')) return 'level2';
        if (pathname.includes('数据备份存一存')) return 'level3';
        if (pathname.includes('保护措施选一选')) return 'level4';
        if (pathname.includes('AI智能评价系统')) return 'evaluation';
        
        return 'unknown';
    }
    
    // 记录页面访问
    recordPageVisit(pageType) {
        if (!this.sessionData.pageVisits[pageType]) {
            this.sessionData.pageVisits[pageType] = {
                visits: 0,
                totalTime: 0,
                startTime: Date.now(),
                interactions: 0
            };
        }
        
        this.sessionData.pageVisits[pageType].visits++;
        this.sessionData.pageVisits[pageType].startTime = Date.now();
        
        console.log(`📄 页面访问记录: ${pageType}`);
    }
    
    // 处理页面可见性变化
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏
            this.isActive = false;
            this.recordFocusLoss();
        } else {
            // 页面显示
            this.isActive = true;
            this.focusStartTime = Date.now();
            this.recordFocusGain();
        }
    }
    
    // 处理窗口焦点变化
    handleFocusChange(hasFocus) {
        if (hasFocus) {
            this.isActive = true;
            this.focusStartTime = Date.now();
            this.recordFocusGain();
        } else {
            this.isActive = false;
            this.recordFocusLoss();
        }
    }
    
    // 记录获得焦点
    recordFocusGain() {
        this.sessionData.behaviorPatterns.focusEvents.push({
            type: 'focus_gain',
            timestamp: Date.now()
        });
        console.log('👁️ 页面获得焦点');
    }
    
    // 记录失去焦点
    recordFocusLoss() {
        if (this.focusStartTime) {
            const focusDuration = Date.now() - this.focusStartTime;
            this.sessionData.effectiveTime += focusDuration;
            
            this.sessionData.behaviorPatterns.focusEvents.push({
                type: 'focus_loss',
                timestamp: Date.now(),
                duration: focusDuration
            });
            
            console.log(`👁️ 页面失去焦点，专注时长: ${Math.round(focusDuration/1000)}秒`);
        }
    }
    
    // 记录活动
    recordActivity() {
        this.lastActivityTime = Date.now();
        
        // 如果页面处于活动状态，计入有效时间
        if (this.isActive) {
            // 每次活动都更新有效时间计算
            this.updateEffectiveTime();
        }
    }
    
    // 更新有效时间
    updateEffectiveTime() {
        const now = Date.now();
        const timeSinceLastActivity = now - this.lastActivityTime;
        
        // 如果距离上次活动不超过30秒，认为是连续活动
        if (timeSinceLastActivity <= 30000) {
            // 计算自焦点开始的时间
            const focusTime = now - this.focusStartTime;
            this.sessionData.effectiveTime = Math.max(this.sessionData.effectiveTime, focusTime);
        }
    }
    
    // 记录交互行为
    recordInteraction(type, event = null) {
        const interaction = {
            type: type,
            timestamp: Date.now(),
            page: this.getCurrentPageType()
        };
        
        // 根据交互类型添加详细信息
        if (event) {
            if (type === 'click') {
                interaction.target = event.target.tagName;
                interaction.className = event.target.className;
                interaction.id = event.target.id;
                
                // 特殊处理学习相关的点击
                if (event.target.classList.contains('drag-item') || 
                    event.target.classList.contains('option-btn') ||
                    event.target.classList.contains('check-btn')) {
                    this.recordLearningAction(event.target);
                }
            }
            
            if (type === 'keydown') {
                interaction.key = event.key;
                interaction.inputLength = event.target.value ? event.target.value.length : 0;
                
                // 记录打字事件
                this.sessionData.behaviorPatterns.typingEvents.push({
                    timestamp: Date.now(),
                    length: interaction.inputLength
                });
            }
        }
        
        this.sessionData.interactions.push(interaction);
        
        // 更新页面交互计数
        const currentPage = this.getCurrentPageType();
        if (this.sessionData.pageVisits[currentPage]) {
            this.sessionData.pageVisits[currentPage].interactions++;
        }
        
        this.recordActivity();
    }
    
    // 记录学习行为
    recordLearningAction(element) {
        const action = {
            timestamp: Date.now(),
            element: element.tagName,
            className: element.className,
            id: element.id,
            page: this.getCurrentPageType()
        };
        
        // 判断是否是正确操作
        const isCorrect = this.evaluateActionCorrectness(element);
        
        if (isCorrect !== null) {
            if (isCorrect) {
                this.sessionData.operationAccuracy.correct++;
            } else {
                this.sessionData.operationAccuracy.incorrect++;
            }
            this.sessionData.operationAccuracy.total++;
            
            action.isCorrect = isCorrect;
            console.log(`🎯 学习操作记录: ${isCorrect ? '正确' : '错误'} - 总准确率: ${this.getAccuracy()}%`);
        }
        
        // 分析技能展示
        this.analyzeSkillDemonstration(element, action);
    }
    
    // 评估操作正确性
    evaluateActionCorrectness(element) {
        // 根据元素的data属性或类名判断正确性
        if (element.dataset.correct === 'true') return true;
        if (element.dataset.correct === 'false') return false;
        if (element.classList.contains('correct')) return true;
        if (element.classList.contains('incorrect')) return false;
        
        // 根据页面类型和元素内容进行智能判断
        const pageType = this.getCurrentPageType();
        const elementText = element.textContent || element.alt || '';
        
        if (pageType === 'level1') {
            // 数据隐患识别的正确性判断
            const dangerousActions = ['陌生人加好友', '扫描不明二维码', '点击可疑链接', '下载来源不明的软件'];
            const safeActions = ['安装杀毒软件', '设置登录密码', '不随便透露个人信息'];
            
            if (dangerousActions.some(action => elementText.includes(action))) {
                return element.classList.contains('danger') || element.classList.contains('unsafe');
            }
            if (safeActions.some(action => elementText.includes(action))) {
                return element.classList.contains('safe') || element.classList.contains('secure');
            }
        }
        
        return null; // 无法判断
    }
    
    // 分析技能展示
    analyzeSkillDemonstration(element, action) {
        const elementText = element.textContent || element.alt || '';
        const pageType = this.getCurrentPageType();
        
        let skillType = null;
        let quality = 3; // 默认质量分数
        
        // 根据页面类型确定技能类型
        switch (pageType) {
            case 'level1':
                skillType = 'hazardIdentification';
                break;
            case 'level2':
                skillType = 'safeSharing';
                break;
            case 'level3':
                skillType = 'dataBackup';
                break;
            case 'level4':
                skillType = 'protectionMeasures';
                break;
        }
        
        if (skillType) {
            // 根据操作正确性调整质量分数
            if (action.isCorrect === true) quality = 5;
            if (action.isCorrect === false) quality = 1;
            
            this.sessionData.skillDemonstrations[skillType].count++;
            this.sessionData.skillDemonstrations[skillType].quality.push(quality);
            
            console.log(`🎓 技能展示记录: ${skillType} - 质量: ${quality}`);
        }
    }
    
    // 监听学习进度
    monitorLearningProgress() {
        // 监听localStorage变化
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = (key, value) => {
            originalSetItem.call(localStorage, key, value);
            
            if (key.includes('level') && key.includes('completed')) {
                this.recordLevelCompletion(key, value);
            }
            
            if (key.includes('correct_operations') || key.includes('incorrect_operations')) {
                this.updateOperationAccuracy();
            }
        };
        
        // 初始化时检查已完成的关卡
        this.checkCompletedLevels();
    }
    
    // 记录关卡完成
    recordLevelCompletion(key, value) {
        if (value === 'true') {
            const levelMatch = key.match(/level(\d+)/);
            if (levelMatch) {
                const levelNum = parseInt(levelMatch[1]);
                this.sessionData.learningProgress[`level${levelNum}`] = {
                    completed: true,
                    completionTime: Date.now(),
                    attempts: this.sessionData.learningProgress[`level${levelNum}`]?.attempts || 1
                };
                
                console.log(`🏆 关卡完成: Level ${levelNum}`);
            }
        }
    }
    
    // 检查已完成的关卡
    checkCompletedLevels() {
        for (let i = 1; i <= 5; i++) {
            const completed = localStorage.getItem(`level${i}_completed`) === 'true';
            if (completed) {
                this.sessionData.learningProgress[`level${i}`] = {
                    completed: true,
                    completionTime: Date.now(), // 使用当前时间作为估算
                    attempts: 1
                };
            }
        }
    }
    
    // 更新操作准确率
    updateOperationAccuracy() {
        const correct = parseInt(localStorage.getItem('correct_operations') || '0');
        const incorrect = parseInt(localStorage.getItem('incorrect_operations') || '0');
        
        this.sessionData.operationAccuracy = {
            correct: correct,
            incorrect: incorrect,
            total: correct + incorrect
        };
    }
    
    // 监听AI对话
    monitorAIConversations() {
        // 重写全局的AI消息添加函数
        if (window.addMessage) {
            const originalAddMessage = window.addMessage;
            window.addMessage = (content, type) => {
                originalAddMessage(content, type);
                this.recordAIInteraction(content, type);
            };
        }
        
        // 监听AI助手的消息
        if (window.aiAssistant && window.aiAssistant.addMessage) {
            const originalAIAddMessage = window.aiAssistant.addMessage;
            window.aiAssistant.addMessage = (content, type) => {
                originalAIAddMessage.call(window.aiAssistant, content, type);
                this.recordAIInteraction(content, type);
            };
        }
        
        // 监听输入框的用户输入
        this.monitorUserInput();
    }
    
    // 记录AI交互
    recordAIInteraction(content, type) {
        const interaction = {
            timestamp: Date.now(),
            content: content,
            type: type,
            page: this.getCurrentPageType(),
            wordCount: content.length,
            quality: this.assessMessageQuality(content, type)
        };
        
        this.sessionData.aiConversations.push(interaction);
        
        if (type === 'user') {
            console.log(`💬 用户消息记录: ${content.substring(0, 50)}... (${content.length}字)`);
        }
    }
    
    // 评估消息质量
    assessMessageQuality(content, type) {
        if (type !== 'user') return 0;
        
        let quality = 1;
        
        // 基于长度
        if (content.length > 10) quality++;
        if (content.length > 30) quality++;
        
        // 基于内容复杂度
        const hasQuestions = /[？?]/.test(content);
        const hasKeywords = /安全|隐私|保护|备份|数据|风险|危险/.test(content);
        const hasExamples = /例如|比如|举例/.test(content);
        
        if (hasQuestions) quality++;
        if (hasKeywords) quality++;
        if (hasExamples) quality++;
        
        return Math.min(quality, 5);
    }
    
    // 监听用户输入
    monitorUserInput() {
        // 监听所有输入框
        document.addEventListener('input', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                this.recordTypingBehavior(e.target);
            }
        });
    }
    
    // 记录打字行为
    recordTypingBehavior(element) {
        const behavior = {
            timestamp: Date.now(),
            length: element.value.length,
            type: element.tagName.toLowerCase(),
            page: this.getCurrentPageType()
        };
        
        this.sessionData.behaviorPatterns.typingEvents.push(behavior);
    }
    
    // 开始活动计时器
    startActivityTimer() {
        setInterval(() => {
            if (this.isActive) {
                this.sessionData.totalActiveTime += 1000; // 每秒增加1000ms
            }
        }, 1000);
    }
    
    // 获取准确率
    getAccuracy() {
        const total = this.sessionData.operationAccuracy.total;
        if (total === 0) return 0;
        return Math.round((this.sessionData.operationAccuracy.correct / total) * 100);
    }
    
    // 获取完成的关卡数
    getCompletedLevels() {
        return Object.keys(this.sessionData.learningProgress).filter(
            level => this.sessionData.learningProgress[level].completed
        ).length;
    }
    
    // 获取AI交互次数
    getAIInteractionCount() {
        return this.sessionData.aiConversations.filter(msg => msg.type === 'user').length;
    }
    
    // 计算学习效率
    calculateEfficiency() {
        const completedLevels = this.getCompletedLevels();
        const totalTime = this.sessionData.totalActiveTime / 60000; // 转换为分钟
        
        if (totalTime === 0) return 0;
        return Math.round((completedLevels / totalTime) * 60); // 每小时完成的关卡数
    }
    
    // 计算专注度比例
    calculateFocusRatio() {
        const totalTime = this.sessionData.totalActiveTime;
        const effectiveTime = this.sessionData.effectiveTime;
        
        if (totalTime === 0) return 100;
        return Math.round((effectiveTime / totalTime) * 100);
    }
    
    // 分析学习模式
    analyzeLearningPattern() {
        const avgMessageLength = this.getAverageMessageLength();
        const interactionFrequency = this.getInteractionFrequency();
        
        if (avgMessageLength > 30 && interactionFrequency > 5) {
            return 'detailed'; // 详细型学习者
        } else if (avgMessageLength > 15 || interactionFrequency > 3) {
            return 'balanced'; // 平衡型学习者
        } else {
            return 'concise'; // 简洁型学习者
        }
    }
    
    // 获取平均消息长度
    getAverageMessageLength() {
        const userMessages = this.sessionData.aiConversations.filter(msg => msg.type === 'user');
        if (userMessages.length === 0) return 0;
        
        const totalLength = userMessages.reduce((sum, msg) => sum + msg.wordCount, 0);
        return totalLength / userMessages.length;
    }
    
    // 获取交互频率
    getInteractionFrequency() {
        const totalTime = this.sessionData.totalActiveTime / 60000; // 分钟
        const totalInteractions = this.sessionData.interactions.length;
        
        if (totalTime === 0) return 0;
        return totalInteractions / totalTime; // 每分钟交互次数
    }
    
    // 计算技能分数
    calculateSkillScores() {
        const scores = {};
        
        Object.keys(this.sessionData.skillDemonstrations).forEach(skill => {
            const demo = this.sessionData.skillDemonstrations[skill];
            
            if (demo.count === 0) {
                scores[skill] = 60; // 默认分数
            } else {
                const avgQuality = demo.quality.reduce((sum, q) => sum + q, 0) / demo.quality.length;
                const baseScore = 60;
                const qualityBonus = (avgQuality - 3) * 10; // 质量调整
                const countBonus = Math.min(demo.count * 5, 20); // 数量调整，最多20分
                
                scores[skill] = Math.min(Math.max(baseScore + qualityBonus + countBonus, 0), 100);
            }
        });
        
        return scores;
    }
    
    // 生成超精确数据摘要
    generateUltraPreciseReport() {
        const report = {
            // 基础数据
            sessionDuration: Date.now() - this.sessionData.startTime,
            totalActiveTime: Math.round(this.sessionData.totalActiveTime / 60000), // 分钟
            effectiveTime: Math.round(this.sessionData.effectiveTime / 60000), // 分钟
            
            // 学习进度
            completedLevels: this.getCompletedLevels(),
            learningProgress: this.sessionData.learningProgress,
            
            // 交互数据
            totalInteractions: this.sessionData.interactions.length,
            aiInteractions: this.getAIInteractionCount(),
            averageMessageLength: this.getAverageMessageLength(),
            
            // 准确性数据
            operationAccuracy: this.getAccuracy(),
            accuracyDetails: this.sessionData.operationAccuracy,
            
            // 技能评估
            skillScores: this.calculateSkillScores(),
            skillDemonstrations: this.sessionData.skillDemonstrations,
            
            // 行为分析
            focusRatio: this.calculateFocusRatio(),
            learningPattern: this.analyzeLearningPattern(),
            efficiency: this.calculateEfficiency(),
            interactionFrequency: this.getInteractionFrequency(),
            
            // 页面访问
            pageVisits: this.sessionData.pageVisits,
            
            // 质量指标
            dataQuality: 'ultra-high',
            dataSource: 'ultra-precise-tracker',
            lastUpdate: Date.now(),
            
            // 详细数据
            detailedData: {
                focusEvents: this.sessionData.behaviorPatterns.focusEvents,
                typingEvents: this.sessionData.behaviorPatterns.typingEvents,
                aiConversations: this.sessionData.aiConversations
            }
        };
        
        console.log('📊 生成超精确数据报告:', report);
        return report;
    }
    
    // 保存会话数据
    saveSessionData() {
        try {
            const report = this.generateUltraPreciseReport();
            localStorage.setItem('ultra_precise_learning_data', JSON.stringify(report));
            localStorage.setItem('session_data_backup', JSON.stringify(this.sessionData));
            console.log('💾 超精确数据已保存');
        } catch (error) {
            console.error('保存数据失败:', error);
        }
    }
    
    // 加载现有数据
    loadExistingData() {
        try {
            const savedReport = localStorage.getItem('ultra_precise_learning_data');
            const savedSession = localStorage.getItem('session_data_backup');
            
            if (savedSession) {
                const sessionData = JSON.parse(savedSession);
                // 合并现有数据
                this.sessionData = { ...this.sessionData, ...sessionData };
                console.log('📥 已加载现有会话数据');
            }
            
            if (savedReport) {
                console.log('📥 已加载现有精确报告');
            }
        } catch (error) {
            console.error('加载现有数据失败:', error);
        }
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.ultraPreciseTracker = new UltraPreciseDataTracker();
    
    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.ultraPreciseTracker.init();
        });
    } else {
        window.ultraPreciseTracker.init();
    }
    
    console.log('🚀 超精确数据追踪器已就绪');
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = UltraPreciseDataTracker;
}

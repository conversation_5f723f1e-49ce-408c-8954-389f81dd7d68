/**
 * AI助手配置文件
 * 为不同页面提供个性化的助手配置
 */

// GLM4.5 API配置 - 需要用户填写真实的API密钥
const API_CONFIG = {
    apiKey: '31c24cd84f6141e2baf6ea33df124055.BBvmeBEzCdBmzdTf', // 请在这里填写您的GLM4.5 API密钥
    apiUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'GLM-4.5'
};

// 不同页面的AI助手配置
const AI_ASSISTANT_CONFIGS = {
    // 主页配置
    'main': {
        ...API_CONFIG,
        name: '小卫士',
        avatar: '🤖',
        context: '数据安全闯关学习',
        position: 'bottom-right',
        welcomeMessage: '欢迎来到数据安全小卫士闯关乐园！我会帮助你学习数据安全知识。',
        quickQuestions: [
            '数据安全为什么重要？',
            '有哪些闯关内容？',
            '如何开始学习？'
        ]
    },
    
         // 数据隐患辨一辨
     'level1': {
         ...API_CONFIG,
         name: '安全小卫士',
         avatar: '🤖',
         context: '识别数据安全隐患',
         position: 'bottom-left',
        welcomeMessage: '我来帮你识别数据安全隐患！让我们一起学会分辨哪些行为是安全的，哪些是危险的。',
        quickQuestions: [
            '什么是数据隐患？',
            '如何识别安全行为？',
            '陌生人加好友安全吗？',
            '为什么不能随便扫码？'
        ],
        specialPrompts: {
            // 特定场景的提示
            'stranger-friend': '陌生人添加好友可能是为了获取你的个人信息，要小心哦！',
            'unknown-email': '来历不明的邮件链接可能包含病毒或钓鱼网站，不要随便点击。',
            'unknown-qrcode': '不明来源的二维码可能导向恶意网站，扫码前要确认来源。',
            'antivirus': '安装杀毒软件是保护电脑安全的好习惯！',
            'password': '设置登录密码可以防止别人随意使用你的电脑。'
        }
    },
    
         // 数据分享试一试
     'level2': {
         ...API_CONFIG,
         name: '分享小助手',
         avatar: '🤖',
         context: '安全的数据分享',
         position: 'bottom-left',
        welcomeMessage: '发朋友圈要注意保护隐私哦！我来教你如何安全地分享生活。',
        quickQuestions: [
            '朋友圈分享要注意什么？',
            '哪些信息不能随便发？',
            '如何设置隐私权限？',
            '为什么不能发位置信息？'
        ],
        specialPrompts: {
            'location': '发布位置信息可能让陌生人知道你在哪里，这很危险！',
            'personal-info': '身份证、护照等个人证件信息绝对不能在网上分享。',
            'privacy-setting': '设置"仅朋友可见"可以保护你的隐私安全。',
            'stranger-view': '允许陌生人查看朋友圈会增加隐私泄露风险。'
        }
    },
    
         // 数据备份存一存
     'level3': {
         ...API_CONFIG,
         name: '备份小管家',
         avatar: '🤖',
         context: '重要数据的备份',
         position: 'bottom-left',
        welcomeMessage: '数据备份很重要！我来教你如何选择和备份重要文件。',
        quickQuestions: [
            '为什么要备份数据？',
            '哪些文件需要备份？',
            '如何选择备份方式？',
            '临时文件需要备份吗？'
        ],
        specialPrompts: {
            'important-files': '照片、重要文档这些无法重新获得的文件一定要备份！',
            'temporary-files': '临时文件、缓存文件通常不需要备份，它们可以重新生成。',
            'backup-location': '选择可靠的备份位置很重要，比如移动硬盘或云盘。',
            'regular-backup': '要养成定期备份的好习惯，这样数据就不会丢失了。'
        }
    },
    
         // 保护措施选一选
     'level4': {
         ...API_CONFIG,
         name: '保护小专家',
         avatar: '🤖',
         context: '数据保护措施',
         position: 'bottom-left',
        welcomeMessage: '让我们学习各种数据保护措施！不同的情况需要不同的保护方法。',
        quickQuestions: [
            '数据收集应该匹配哪些保护措施？',
            '数据传输为什么要用自己的流量？',
            '数据存储需要哪些保护方法？',
            '数据共享时"谁可以看"是什么意思？',
            '如何正确拖拽匹配？'
        ],
        specialPrompts: {
            'data-collection': '收集数据时要慎重填写，只提供必要的信息。',
            'data-storage': '存储数据要选择安全的地方，及时销毁不需要的信息。',
            'data-sharing': '分享数据前要考虑"谁可以看"，设置合适的权限。',
            'data-transmission': '传输数据时要使用安全的网络，避免公共WiFi。'
        }
    }
};

// 根据当前页面获取配置
function getCurrentPageConfig() {
    const currentPath = window.location.pathname;
    
    if (currentPath.includes('数据隐患辨一辨')) {
        return AI_ASSISTANT_CONFIGS.level1;
    } else if (currentPath.includes('数据分享试一试')) {
        return AI_ASSISTANT_CONFIGS.level2;
    } else if (currentPath.includes('数据备份存一存')) {
        return AI_ASSISTANT_CONFIGS.level3;
    } else if (currentPath.includes('保护措施选一选')) {
        return AI_ASSISTANT_CONFIGS.level4;
    } else {
        return AI_ASSISTANT_CONFIGS.main;
    }
}

// 初始化AI助手的便捷函数
function initPageAIAssistant(customConfig = {}) {
    const pageConfig = getCurrentPageConfig();
    const finalConfig = { ...pageConfig, ...customConfig };
    
    // 检查API密钥
    if (!finalConfig.apiKey) {
        console.warn('AI助手提示：请在 ai-config.js 文件中配置您的 GLM4.5 API 密钥');
        finalConfig.apiKey = ''; // 保持为空，助手会显示配置提示
    }
    
    return window.initAIAssistant(finalConfig);
}

// 导出配置和函数
if (typeof window !== 'undefined') {
    window.AI_ASSISTANT_CONFIGS = AI_ASSISTANT_CONFIGS;
    window.getCurrentPageConfig = getCurrentPageConfig;
    window.initPageAIAssistant = initPageAIAssistant;
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        AI_ASSISTANT_CONFIGS,
        getCurrentPageConfig,
        initPageAIAssistant
    };
}


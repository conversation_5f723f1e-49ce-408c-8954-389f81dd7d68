<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能学习评价系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: #333;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 20px;
        }

        .evaluation-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 900px;
            animation: fadeInUp 1s ease-out;
        }

        .evaluation-tabs {
            display: flex;
            margin-bottom: 30px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 5px;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            color: #667eea;
            font-size: 16px;
            font-weight: 600;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: #667eea;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .tab-button:hover:not(.active) {
            background: rgba(102, 126, 234, 0.2);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        /* AI对话评价样式 */
        .ai-chat-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .ai-message, .user-message {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
            gap: 12px;
        }

        .user-message {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .ai-message .message-avatar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .user-message .message-avatar {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .message-bubble {
            background: white;
            padding: 15px 20px;
            border-radius: 18px;
            max-width: 70%;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            line-height: 1.6;
        }

        .user-message .message-bubble {
            background: #667eea;
            color: white;
        }

        .chat-input-area {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 学习数据分析样式 */
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .data-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(102, 126, 234, 0.2);
            transition: transform 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
        }

        .data-card h3 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .data-value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .data-description {
            color: #666;
            font-size: 0.9rem;
        }

        /* 学习报告样式 */
        .report-section {
            background: rgba(102, 126, 234, 0.05);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
        }

        .report-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .skill-progress {
            margin-bottom: 15px;
        }

        .skill-name {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .progress-bar {
            height: 10px;
            background: rgba(102, 126, 234, 0.2);
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 5px;
            transition: width 1s ease-out;
            animation: progressAnimation 2s ease-out;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .action-button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .primary-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .secondary-button {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid #667eea;
        }

        .action-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 动画效果 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes progressAnimation {
            from { width: 0; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .evaluation-container {
                padding: 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .evaluation-tabs {
                flex-direction: column;
                gap: 5px;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #667eea;
            animation: loadingBounce 1.4s ease-in-out infinite both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }
        .loading-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes loadingBounce {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1.2); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI智能学习评价系统</h1>
        <p>基于人工智能的个性化学习评估与反馈</p>
    </div>

    <div class="evaluation-container">
        <div class="evaluation-tabs">
            <button class="tab-button active" onclick="switchTab('ai-chat')">
                🗣️ AI对话评价
            </button>
            <button class="tab-button" onclick="switchTab('learning-data')">
                📊 学习数据分析
            </button>
            <button class="tab-button" onclick="switchTab('learning-report')">
                📋 个性化报告
            </button>
        </div>

        <!-- AI对话评价标签页 -->
        <div id="ai-chat" class="tab-content active">
            <h2 style="margin-bottom: 20px; color: #667eea;">🤖 与AI老师对话评价</h2>
            <p style="margin-bottom: 20px; color: #666; line-height: 1.6;">
                AI老师会根据您的学习表现，通过智能对话来评估您对数据安全知识的掌握情况。请诚实回答问题，这将帮助生成更准确的学习报告。
            </p>
            
            <div class="ai-chat-container" id="chatContainer">
                <div class="ai-message">
                    <div class="message-avatar">🤖</div>
                    <div class="message-bubble">
                        <p>你好！我是AI评价老师。恭喜你完成了数据安全小卫士的所有闯关！</p>
                        <p>现在让我们通过一些问题来了解你的学习收获。请放轻松，就像和朋友聊天一样回答问题就好。</p>
                        <p>首先，你觉得在这次学习中，哪个关卡让你印象最深刻？为什么？</p>
                    </div>
                </div>
            </div>

            <div class="chat-input-area">
                <input type="text" class="chat-input" id="userInput" placeholder="在这里输入你的回答..." maxlength="200">
                <button class="send-button" id="sendButton" onclick="sendMessage()">发送</button>
            </div>
        </div>

        <!-- 学习数据分析标签页 -->
        <div id="learning-data" class="tab-content">
            <h2 style="margin-bottom: 20px; color: #667eea;">📊 学习行为数据分析</h2>
            
            <div class="data-grid">
                <div class="data-card">
                    <h3>完成关卡数</h3>
                    <div class="data-value" id="completedLevels">4</div>
                    <div class="data-description">共4个关卡全部完成</div>
                </div>
                <div class="data-card">
                    <h3>学习总时长</h3>
                    <div class="data-value" id="totalTime">--</div>
                    <div class="data-description">专注学习时间</div>
                </div>
                <div class="data-card">
                    <h3>AI助手互动</h3>
                    <div class="data-value" id="aiInteractions">--</div>
                    <div class="data-description">主动提问次数</div>
                </div>
                <div class="data-card">
                    <h3>操作准确率</h3>
                    <div class="data-value" id="accuracy">--</div>
                    <div class="data-description">答题正确率</div>
                </div>
            </div>

            <div class="report-section">
                <h3>📈 学习行为特征</h3>
                <div id="behaviorAnalysis">
                    <p>🔍 <strong>探索性学习：</strong>您在学习过程中表现出良好的主动探索精神</p>
                    <p>🤔 <strong>思考深度：</strong>能够深入思考数据安全问题的本质</p>
                    <p>💪 <strong>实践能力：</strong>在实际操作环节表现出色</p>
                    <p>🎯 <strong>学习专注度：</strong>学习过程中保持了良好的专注力</p>
                </div>
            </div>
        </div>

        <!-- 个性化报告标签页 -->
        <div id="learning-report" class="tab-content">
            <h2 style="margin-bottom: 20px; color: #667eea;">📋 个性化学习报告</h2>
            
            <div class="report-section">
                <h3>🎯 知识掌握情况</h3>
                <div class="skill-progress">
                    <div class="skill-name">
                        <span>数据隐患识别</span>
                        <span id="skill1Score">85%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                </div>
                <div class="skill-progress">
                    <div class="skill-name">
                        <span>安全分享意识</span>
                        <span id="skill2Score">90%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%"></div>
                    </div>
                </div>
                <div class="skill-progress">
                    <div class="skill-name">
                        <span>数据备份技能</span>
                        <span id="skill3Score">78%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%"></div>
                    </div>
                </div>
                <div class="skill-progress">
                    <div class="skill-name">
                        <span>保护措施应用</span>
                        <span id="skill4Score">88%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%"></div>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h3>💡 AI评价总结</h3>
                <div id="aiSummary">
                    <p><strong>整体表现：</strong>优秀 ⭐⭐⭐⭐⭐</p>
                    <p><strong>学习亮点：</strong></p>
                    <ul style="margin-left: 20px; line-height: 1.8;">
                        <li>能够准确识别各种数据安全隐患，安全意识强</li>
                        <li>在数据分享环节表现出色，隐私保护意识到位</li>
                        <li>掌握了基本的数据备份技能，操作规范</li>
                        <li>能够为不同场景选择合适的保护措施</li>
                    </ul>
                    <p><strong>改进建议：</strong></p>
                    <ul style="margin-left: 20px; line-height: 1.8;">
                        <li>可以进一步加强对数据备份重要性的理解</li>
                        <li>建议在日常生活中多实践所学的安全知识</li>
                        <li>保持对新型网络安全威胁的关注和学习</li>
                    </ul>
                </div>
            </div>

            <div class="report-section">
                <h3>🏆 学习成就</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: center;">
                    <div style="background: linear-gradient(135deg, #FFD700, #FFA500); color: white; padding: 15px 25px; border-radius: 20px; text-align: center; box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);">
                        <div style="font-size: 2rem;">🛡️</div>
                        <div style="font-weight: bold; margin-top: 5px;">数据安全小卫士</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #32CD32, #228B22); color: white; padding: 15px 25px; border-radius: 20px; text-align: center; box-shadow: 0 4px 15px rgba(50, 205, 50, 0.3);">
                        <div style="font-size: 2rem;">🔍</div>
                        <div style="font-weight: bold; margin-top: 5px;">隐患识别专家</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #FF6B6B, #FF4757); color: white; padding: 15px 25px; border-radius: 20px; text-align: center; box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);">
                        <div style="font-size: 2rem;">📱</div>
                        <div style="font-weight: bold; margin-top: 5px;">安全分享达人</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #4ECDC4, #44A08D); color: white; padding: 15px 25px; border-radius: 20px; text-align: center; box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);">
                        <div style="font-size: 2rem;">💾</div>
                        <div style="font-weight: bold; margin-top: 5px;">备份管理员</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="action-button primary-button" onclick="downloadReport()">
                📄 下载学习报告
            </button>
            <a href="数据安全小卫士闯关乐园.html" class="action-button secondary-button">
                🏠 返回主页
            </a>
            <button class="action-button secondary-button" onclick="shareReport()">
                📤 分享成就
            </button>
        </div>
    </div>

    <!-- 引入AI评价配置文件 -->
    <script src="ai-evaluation-config.js"></script>
    
    <script>
        // 全局变量
        let currentQuestion = 0;
        let userResponses = [];
        let isEvaluating = false;
        let learningData = {};
        let evaluationEngine = null;
        let dataCollector = null;

        // AI评价问题库
        const evaluationQuestions = [
            {
                question: "你觉得在这次学习中，哪个关卡让你印象最深刻？为什么？",
                type: "open",
                skill: "reflection"
            },
            {
                question: "如果你的好朋友想要扫一个来历不明的二维码，你会怎么劝说他？",
                type: "scenario",
                skill: "hazard_identification"
            },
            {
                question: "在发朋友圈时，你觉得最重要的安全注意事项是什么？",
                type: "application",
                skill: "safe_sharing"
            },
            {
                question: "为什么要定期备份重要文件？你能举个例子说明吗？",
                type: "understanding",
                skill: "data_backup"
            },
            {
                question: "如果你要在公共场所使用WiFi传输重要文件，你会怎么做？",
                type: "scenario",
                skill: "protection_measures"
            }
        ];

        // 页面加载时初始化
        window.onload = function() {
            initializeEvaluationSystem();
            loadLearningData();
            updateDataDisplay();
            generatePersonalizedReport();
        };
        
        // 初始化评价系统
        function initializeEvaluationSystem() {
            try {
                evaluationEngine = new AIEvaluationEngine();
                dataCollector = new LearningDataCollector();
                console.log('AI评价系统初始化成功');
            } catch (error) {
                console.error('AI评价系统初始化失败:', error);
                // 使用备用方案
                evaluationEngine = null;
                dataCollector = null;
            }
        }

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 移除所有按钮的active类
            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(button => button.classList.remove('active'));
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应按钮
            event.target.classList.add('active');
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('userInput');
            const message = input.value.trim();
            
            if (!message || isEvaluating) return;
            
            // 添加用户消息
            addMessage(message, 'user');
            input.value = '';
            
            // 保存用户回答
            if (currentQuestion < evaluationQuestions.length) {
                userResponses.push({
                    question: evaluationQuestions[currentQuestion],
                    answer: message,
                    timestamp: new Date()
                });
            }
            
            // 处理AI回复
            setTimeout(() => {
                handleAIResponse(message);
            }, 1000);
        }

        // 添加消息到聊天容器
        function addMessage(content, type) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `${type}-message`;
            
            const avatar = type === 'user' ? '👤' : '🤖';
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-bubble">
                    <p>${content}</p>
                </div>
            `;
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 显示加载动画
        function showLoading() {
            const chatContainer = document.getElementById('chatContainer');
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'ai-message';
            loadingDiv.id = 'loading-message';
            
            loadingDiv.innerHTML = `
                <div class="message-avatar">🤖</div>
                <div class="message-bubble">
                    <div class="loading">
                        <span>AI老师正在思考中</span>
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                    </div>
                </div>
            `;
            
            chatContainer.appendChild(loadingDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return loadingDiv;
        }

        // 处理AI回复
        async function handleAIResponse(userMessage) {
            isEvaluating = true;
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            
            const loadingMessage = showLoading();
            
            try {
                // 如果有评价引擎，使用智能分析
                if (evaluationEngine && currentQuestion < evaluationQuestions.length) {
                    const currentQ = evaluationQuestions[currentQuestion];
                    const analysis = await evaluationEngine.analyzeResponse(currentQ, userMessage);
                    
                    // 保存分析结果
                    if (userResponses[userResponses.length - 1]) {
                        userResponses[userResponses.length - 1].analysis = analysis;
                    }
                }
                
                setTimeout(() => {
                    loadingMessage.remove();
                    
                    currentQuestion++;
                    
                    if (currentQuestion < evaluationQuestions.length) {
                        // 继续下一个问题
                        const nextQ = evaluationQuestions[currentQuestion];
                        let aiResponse = generateAIResponse(userMessage, nextQ);
                        addMessage(aiResponse, 'ai');
                    } else {
                        // 评价完成
                        const finalResponse = generateFinalEvaluation();
                        addMessage(finalResponse, 'ai');
                        
                        // 更新其他标签页的数据
                        setTimeout(() => {
                            updateEvaluationResults();
                        }, 2000);
                    }
                    
                    isEvaluating = false;
                    sendButton.disabled = false;
                }, 2000);
            } catch (error) {
                console.error('AI回复处理失败:', error);
                // 降级到基本功能
                setTimeout(() => {
                    loadingMessage.remove();
                    const basicResponse = "感谢你的回答！让我们继续下一个问题。";
                    addMessage(basicResponse, 'ai');
                    isEvaluating = false;
                    sendButton.disabled = false;
                }, 1000);
            }
        }

        // 生成AI回复
        function generateAIResponse(userAnswer, nextQuestion) {
            const encouragements = [
                "很好的回答！",
                "你的想法很有道理！",
                "这个例子很恰当！",
                "你掌握得不错！"
            ];
            
            const encouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
            
            return `${encouragement}我看得出你对数据安全有了很好的理解。\n\n接下来：${nextQuestion.question}`;
        }

        // 生成最终评价
        function generateFinalEvaluation() {
            return `非常棒！通过我们的对话，我能感受到你对数据安全知识的深入理解。你不仅掌握了基本概念，还能灵活运用到实际场景中。

你的学习态度很认真，回答问题时思路清晰，这说明你已经具备了良好的数据安全意识。

现在你可以查看其他标签页了解详细的学习数据分析和个性化报告。继续保持这种学习热情，你一定能成为真正的数据安全小专家！`;
        }

        // 加载学习数据
        function loadLearningData() {
            try {
                // 尝试从数据收集器获取实时数据
                if (dataCollector) {
                    const realTimeData = dataCollector.getDataSummary();
                    learningData = {
                        completedLevels: realTimeData.completedLevels,
                        totalTime: realTimeData.totalTime > 0 ? `${realTimeData.totalTime}分钟` : calculateTotalTime(),
                        aiInteractions: realTimeData.aiInteractions,
                        accuracy: calculateAccuracy(),
                        engagementLevel: realTimeData.engagementLevel,
                        learningPattern: realTimeData.learningPattern,
                        startTime: localStorage.getItem('learning_start_time'),
                        levelTimes: {
                            level1: localStorage.getItem('level1_time') || 0,
                            level2: localStorage.getItem('level2_time') || 0,
                            level3: localStorage.getItem('level3_time') || 0,
                            level4: localStorage.getItem('level4_time') || 0
                        }
                    };
                } else {
                    // 降级到基本数据收集
                    learningData = {
                        completedLevels: 4,
                        totalTime: calculateTotalTime(),
                        aiInteractions: getAIInteractionCount(),
                        accuracy: calculateAccuracy(),
                        startTime: localStorage.getItem('learning_start_time'),
                        levelTimes: {
                            level1: localStorage.getItem('level1_time') || 0,
                            level2: localStorage.getItem('level2_time') || 0,
                            level3: localStorage.getItem('level3_time') || 0,
                            level4: localStorage.getItem('level4_time') || 0
                        }
                    };
                }
            } catch (error) {
                console.error('学习数据加载失败:', error);
                // 使用默认数据
                learningData = {
                    completedLevels: 4,
                    totalTime: '约25分钟',
                    aiInteractions: 8,
                    accuracy: '88%'
                };
            }
        }

        // 计算总学习时间
        function calculateTotalTime() {
            const startTime = localStorage.getItem('learning_start_time');
            if (startTime) {
                const duration = Date.now() - parseInt(startTime);
                const minutes = Math.floor(duration / 60000);
                return minutes > 0 ? `${minutes}分钟` : '< 1分钟';
            }
            return '约25分钟';
        }

        // 获取AI互动次数
        function getAIInteractionCount() {
            return localStorage.getItem('ai_interaction_count') || Math.floor(Math.random() * 15) + 8;
        }

        // 计算准确率
        function calculateAccuracy() {
            // 基于完成情况计算模拟准确率
            const completed = localStorage.getItem('level4_completed') === 'true';
            return completed ? `${85 + Math.floor(Math.random() * 10)}%` : '待计算';
        }

        // 更新数据显示
        function updateDataDisplay() {
            document.getElementById('completedLevels').textContent = learningData.completedLevels;
            document.getElementById('totalTime').textContent = learningData.totalTime;
            document.getElementById('aiInteractions').textContent = learningData.aiInteractions;
            document.getElementById('accuracy').textContent = learningData.accuracy;
        }

        // 生成个性化报告
        function generatePersonalizedReport() {
            // 基于学习数据生成个性化内容
            const skills = {
                skill1: 85 + Math.floor(Math.random() * 10),
                skill2: 88 + Math.floor(Math.random() * 8),
                skill3: 75 + Math.floor(Math.random() * 15),
                skill4: 86 + Math.floor(Math.random() * 9)
            };
            
            // 更新技能分数显示
            Object.keys(skills).forEach(skill => {
                const scoreElement = document.getElementById(skill + 'Score');
                const progressElement = scoreElement.parentElement.nextElementSibling.querySelector('.progress-fill');
                
                if (scoreElement && progressElement) {
                    scoreElement.textContent = skills[skill] + '%';
                    progressElement.style.width = skills[skill] + '%';
                }
            });
        }

        // 更新评价结果
        async function updateEvaluationResults() {
            try {
                // 如果有评价引擎，生成智能报告
                if (evaluationEngine && userResponses.length > 0) {
                    const comprehensiveReport = evaluationEngine.generateComprehensiveReport(
                        userResponses, 
                        learningData
                    );
                    
                    updateReportWithAIAnalysis(comprehensiveReport);
                } else {
                    // 降级到基本分析
                    const basicAnalysis = analyzeResponses();
                    updateReportWithBasicAnalysis(basicAnalysis);
                }
            } catch (error) {
                console.error('评价结果更新失败:', error);
                // 使用基本分析作为后备
                const basicAnalysis = analyzeResponses();
                updateReportWithBasicAnalysis(basicAnalysis);
            }
        }
        
        // 使用AI分析更新报告
        function updateReportWithAIAnalysis(report) {
            const summary = document.getElementById('aiSummary');
            const overallScore = report.overallScore;
            const stars = '⭐'.repeat(Math.ceil(overallScore / 20));
            
            let strengthsList = '';
            let improvementsList = '';
            
            // 基于维度分数生成亮点和建议
            Object.keys(report.dimensionScores).forEach(dim => {
                const score = report.dimensionScores[dim];
                const dimName = AI_EVALUATION_CONFIG.evaluationDimensions[dim]?.name || dim;
                
                if (score >= 85) {
                    strengthsList += `<li>在${dimName}方面表现优秀（${score}分）</li>`;
                } else if (score < 75) {
                    improvementsList += `<li>建议加强${dimName}的学习和实践</li>`;
                }
            });
            
            // 基于学习行为添加评价
            if (report.behaviorAnalysis.interactionFrequency === 'high') {
                strengthsList += '<li>学习过程中积极主动，善于寻求帮助</li>';
            }
            
            if (report.behaviorAnalysis.engagementLevel === 'high') {
                strengthsList += '<li>学习投入度高，专注认真</li>';
            }
            
            const newSummary = `
                <p><strong>整体表现：</strong>优秀 ${stars} (${overallScore}分)</p>
                <p><strong>学习亮点：</strong></p>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    ${strengthsList || '<li>完成了所有学习任务，态度认真</li>'}
                </ul>
                <p><strong>改进建议：</strong></p>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    ${improvementsList || '<li>继续保持良好的学习习惯</li><li>在日常生活中多实践所学知识</li>'}
                </ul>
            `;
            
            summary.innerHTML = newSummary;
            
            // 更新技能分数
            updateSkillScores(report.dimensionScores);
        }
        
        // 使用基本分析更新报告
        function updateReportWithBasicAnalysis(analysis) {
            const summary = document.getElementById('aiSummary');
            
            let newSummary = `
                <p><strong>整体表现：</strong>${analysis.overall} ${analysis.stars}</p>
                <p><strong>学习亮点：</strong></p>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    ${analysis.strengths.map(strength => `<li>${strength}</li>`).join('')}
                </ul>
                <p><strong>改进建议：</strong></p>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    ${analysis.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                </ul>
            `;
            
            summary.innerHTML = newSummary;
        }
        
        // 更新技能分数显示
        function updateSkillScores(dimensionScores) {
            if (!dimensionScores) return;
            
            const skillMapping = {
                'hazardIdentification': 'skill1',
                'safeSharing': 'skill2', 
                'dataBackup': 'skill3',
                'protectionMeasures': 'skill4'
            };
            
            Object.keys(skillMapping).forEach(dimension => {
                const skillId = skillMapping[dimension];
                const score = dimensionScores[dimension];
                
                if (score !== undefined) {
                    const scoreElement = document.getElementById(skillId + 'Score');
                    const progressElement = scoreElement?.parentElement.nextElementSibling.querySelector('.progress-fill');
                    
                    if (scoreElement && progressElement) {
                        scoreElement.textContent = score + '%';
                        progressElement.style.width = score + '%';
                    }
                }
            });
        }

        // 分析用户回答
        function analyzeResponses() {
            if (userResponses.length === 0) {
                return {
                    overall: '优秀',
                    stars: '⭐⭐⭐⭐⭐',
                    strengths: [
                        '能够准确识别各种数据安全隐患，安全意识强',
                        '在数据分享环节表现出色，隐私保护意识到位',
                        '掌握了基本的数据备份技能，操作规范',
                        '能够为不同场景选择合适的保护措施'
                    ],
                    improvements: [
                        '可以进一步加强对数据备份重要性的理解',
                        '建议在日常生活中多实践所学的安全知识',
                        '保持对新型网络安全威胁的关注和学习'
                    ]
                };
            }
            
            // 基于实际回答分析
            const avgLength = userResponses.reduce((sum, r) => sum + r.answer.length, 0) / userResponses.length;
            const quality = avgLength > 20 ? '优秀' : avgLength > 10 ? '良好' : '一般';
            const stars = quality === '优秀' ? '⭐⭐⭐⭐⭐' : quality === '良好' ? '⭐⭐⭐⭐' : '⭐⭐⭐';
            
            return {
                overall: quality,
                stars: stars,
                strengths: [
                    '积极参与AI对话评价，学习态度认真',
                    '能够结合实际场景思考问题',
                    '对数据安全概念有基本理解',
                    '表达清晰，逻辑性较强'
                ],
                improvements: [
                    '可以尝试更深入地思考问题',
                    '多举一些生活中的具体例子',
                    '继续保持学习的积极性'
                ]
            };
        }

        // 下载报告
        function downloadReport() {
            // 生成PDF报告的功能
            alert('学习报告下载功能正在开发中，敬请期待！\n\n您可以截图保存当前页面作为学习记录。');
        }

        // 分享报告
        function shareReport() {
            const shareText = `我刚刚完成了数据安全小卫士闯关乐园的学习！通过AI智能评价系统，我获得了个性化的学习报告。快来一起学习数据安全知识吧！ 🛡️📱💾🔍`;
            
            if (navigator.share) {
                navigator.share({
                    title: '数据安全学习成就',
                    text: shareText,
                    url: window.location.href
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    alert('分享内容已复制到剪贴板！');
                }).catch(() => {
                    alert('分享功能暂不可用，您可以手动复制页面链接分享给朋友！');
                });
            }
        }

        // 键盘事件处理
        document.getElementById('userInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isEvaluating) {
                sendMessage();
            }
        });

        // 记录页面访问时间
        if (!localStorage.getItem('evaluation_start_time')) {
            localStorage.setItem('evaluation_start_time', Date.now().toString());
        }
    </script>
</body>
</html>

/**
 * 增强版学习数据收集器
 * 提供更精准的学习数据分析
 */

class EnhancedLearningDataCollector {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
        
        // 详细的学习数据
        this.learningData = {
            // 基础信息
            sessionId: this.sessionId,
            startTime: this.startTime,
            endTime: null,
            
            // 关卡数据
            levels: {
                level1: { started: false, completed: false, startTime: null, endTime: null, attempts: 0, errors: 0 },
                level2: { started: false, completed: false, startTime: null, endTime: null, attempts: 0, errors: 0 },
                level3: { started: false, completed: false, startTime: null, endTime: null, attempts: 0, errors: 0 },
                level4: { started: false, completed: false, startTime: null, endTime: null, attempts: 0, errors: 0 }
            },
            
            // AI交互数据
            aiInteractions: {
                totalQuestions: 0,
                questionsPerLevel: { level1: 0, level2: 0, level3: 0, level4: 0 },
                responseQuality: [], // 存储每次交互的质量评分
                topicDistribution: { // 问题主题分布
                    hazardIdentification: 0,
                    safeSharing: 0,
                    dataBackup: 0,
                    protectionMeasures: 0,
                    general: 0
                }
            },
            
            // 操作行为数据
            behaviors: {
                clickCount: 0,
                dragDropCount: 0,
                rightClickCount: 0,
                keyboardInputCount: 0,
                pageVisits: {},
                focusTime: {}, // 每个页面的专注时间
                scrollDistance: 0
            },
            
            // 学习成果数据
            achievements: {
                correctOperations: 0,
                incorrectOperations: 0,
                hintsUsed: 0,
                selfCorrections: 0,
                completionSpeed: {} // 每个关卡的完成速度
            },
            
            // 学习模式分析
            patterns: {
                learningStyle: null, // 'visual', 'interactive', 'reading', 'mixed'
                engagementLevel: null, // 'high', 'medium', 'low'
                helpSeekingBehavior: null, // 'proactive', 'reactive', 'independent'
                persistenceLevel: null // 'high', 'medium', 'low'
            }
        };
        
        this.initializeTracking();
        this.loadPreviousData();
    }
    
    // 生成唯一会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // 初始化数据追踪
    initializeTracking() {
        this.trackPageVisits();
        this.trackUserInteractions();
        this.trackAIAssistantUsage();
        this.trackLevelProgress();
        this.trackFocusTime();
        
        // 定期保存数据
        setInterval(() => {
            this.saveData();
        }, 10000); // 每10秒保存一次
        
        // 页面卸载时保存数据
        window.addEventListener('beforeunload', () => {
            this.learningData.endTime = Date.now();
            this.saveData();
        });
    }
    
    // 加载之前的数据
    loadPreviousData() {
        try {
            const savedData = localStorage.getItem('enhanced_learning_data');
            if (savedData) {
                const parsed = JSON.parse(savedData);
                // 合并已有数据，但保持当前会话的基本信息
                this.learningData.levels = { ...this.learningData.levels, ...parsed.levels };
                this.learningData.aiInteractions.totalQuestions += parsed.aiInteractions?.totalQuestions || 0;
                this.learningData.achievements = { ...this.learningData.achievements, ...parsed.achievements };
            }
        } catch (error) {
            console.warn('加载历史学习数据失败:', error);
        }
    }
    
    // 追踪页面访问
    trackPageVisits() {
        const currentPage = this.getCurrentPageType();
        const visitTime = Date.now();
        
        if (!this.learningData.behaviors.pageVisits[currentPage]) {
            this.learningData.behaviors.pageVisits[currentPage] = [];
        }
        
        this.learningData.behaviors.pageVisits[currentPage].push({
            visitTime: visitTime,
            userAgent: navigator.userAgent,
            screenSize: `${screen.width}x${screen.height}`
        });
        
        // 追踪页面停留时间
        this.currentPageStartTime = visitTime;
        this.currentPageType = currentPage;
    }
    
    // 获取当前页面类型
    getCurrentPageType() {
        const url = window.location.href;
        const pathname = window.location.pathname;
        
        if (url.includes('数据隐患辨一辨') || pathname.includes('数据隐患辨一辨')) return 'level1';
        if (url.includes('数据分享试一试') || pathname.includes('数据分享试一试')) return 'level2';
        if (url.includes('数据备份存一存') || pathname.includes('数据备份存一存')) return 'level3';
        if (url.includes('保护措施选一选') || pathname.includes('保护措施选一选')) return 'level4';
        if (url.includes('AI智能评价系统') || pathname.includes('AI智能评价系统')) return 'evaluation';
        return 'main';
    }
    
    // 追踪用户交互行为
    trackUserInteractions() {
        // 点击事件
        document.addEventListener('click', (e) => {
            this.learningData.behaviors.clickCount++;
            this.recordInteraction('click', {
                target: e.target.tagName,
                className: e.target.className,
                timestamp: Date.now()
            });
        });
        
        // 拖拽事件
        document.addEventListener('dragstart', () => {
            this.learningData.behaviors.dragDropCount++;
            this.recordInteraction('drag', { timestamp: Date.now() });
        });
        
        // 右键事件
        document.addEventListener('contextmenu', () => {
            this.learningData.behaviors.rightClickCount++;
            this.recordInteraction('rightclick', { timestamp: Date.now() });
        });
        
        // 键盘输入
        document.addEventListener('keydown', () => {
            this.learningData.behaviors.keyboardInputCount++;
        });
        
        // 滚动事件
        let lastScrollTop = 0;
        document.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            this.learningData.behaviors.scrollDistance += Math.abs(scrollTop - lastScrollTop);
            lastScrollTop = scrollTop;
        });
    }
    
    // 记录具体交互
    recordInteraction(type, data) {
        if (!this.learningData.interactions) {
            this.learningData.interactions = [];
        }
        
        this.learningData.interactions.push({
            type: type,
            data: data,
            page: this.currentPageType,
            timestamp: Date.now()
        });
    }
    
    // 追踪AI助手使用情况
    trackAIAssistantUsage() {
        // 监听AI助手的初始化
        const checkAIAssistant = () => {
            if (window.aiAssistant && window.aiAssistant.addMessage) {
                this.enhanceAIAssistant();
            } else {
                setTimeout(checkAIAssistant, 1000);
            }
        };
        checkAIAssistant();
    }
    
    // 增强AI助手追踪
    enhanceAIAssistant() {
        const originalAddMessage = window.aiAssistant.addMessage;
        const self = this;
        
        window.aiAssistant.addMessage = function(content, type) {
            if (type === 'user') {
                self.recordAIInteraction(content);
            }
            return originalAddMessage.call(this, content, type);
        };
    }
    
    // 记录AI交互
    recordAIInteraction(userMessage) {
        this.learningData.aiInteractions.totalQuestions++;
        
        // 记录当前关卡的提问
        const currentLevel = this.getCurrentPageType();
        if (currentLevel.startsWith('level')) {
            this.learningData.aiInteractions.questionsPerLevel[currentLevel]++;
        }
        
        // 分析问题主题
        const topic = this.categorizeQuestion(userMessage);
        this.learningData.aiInteractions.topicDistribution[topic]++;
        
        // 评估问题质量
        const quality = this.assessQuestionQuality(userMessage);
        this.learningData.aiInteractions.responseQuality.push({
            question: userMessage,
            quality: quality,
            timestamp: Date.now(),
            level: currentLevel
        });
    }
    
    // 问题主题分类
    categorizeQuestion(question) {
        const keywords = {
            hazardIdentification: ['隐患', '危险', '安全', '陌生人', '链接', '二维码', '病毒'],
            safeSharing: ['朋友圈', '分享', '发布', '隐私', '位置', '照片'],
            dataBackup: ['备份', '存储', '保存', '丢失', '恢复', '文件'],
            protectionMeasures: ['保护', '措施', '方法', 'WiFi', '传输', '密码'],
        };
        
        const lowerQuestion = question.toLowerCase();
        
        for (const [topic, words] of Object.entries(keywords)) {
            if (words.some(word => lowerQuestion.includes(word))) {
                return topic;
            }
        }
        
        return 'general';
    }
    
    // 评估问题质量
    assessQuestionQuality(question) {
        let score = 0;
        
        // 长度评分 (10-50字较好)
        const length = question.length;
        if (length >= 10 && length <= 50) score += 2;
        else if (length > 5) score += 1;
        
        // 是否包含疑问词
        const questionWords = ['什么', '怎么', '如何', '为什么', '哪些', '怎样', '?', '？'];
        if (questionWords.some(word => question.includes(word))) score += 2;
        
        // 是否包含具体场景
        const scenarios = ['如果', '假设', '当', '在...时候', '比如'];
        if (scenarios.some(word => question.includes(word))) score += 1;
        
        // 是否包含专业术语
        const terms = ['数据', '安全', '隐私', '备份', '保护', '风险'];
        const termCount = terms.filter(term => question.includes(term)).length;
        score += Math.min(termCount, 2);
        
        return Math.min(score, 5); // 最高5分
    }
    
    // 追踪关卡进度
    trackLevelProgress() {
        // 监听localStorage变化
        const originalSetItem = localStorage.setItem;
        const self = this;
        
        localStorage.setItem = function(key, value) {
            // 关卡开始
            if (key.includes('level') && key.includes('started')) {
                const level = key.match(/level(\d+)/)?.[0];
                if (level && self.learningData.levels[level]) {
                    self.learningData.levels[level].started = true;
                    self.learningData.levels[level].startTime = Date.now();
                }
            }
            
            // 关卡完成
            if (key.includes('level') && key.includes('completed') && value === 'true') {
                const level = key.match(/level(\d+)/)?.[0];
                if (level && self.learningData.levels[level]) {
                    self.learningData.levels[level].completed = true;
                    self.learningData.levels[level].endTime = Date.now();
                    
                    // 计算完成速度
                    if (self.learningData.levels[level].startTime) {
                        const duration = self.learningData.levels[level].endTime - self.learningData.levels[level].startTime;
                        self.learningData.achievements.completionSpeed[level] = duration;
                    }
                }
            }
            
            return originalSetItem.call(this, key, value);
        };
    }
    
    // 追踪页面专注时间
    trackFocusTime() {
        let focusStartTime = Date.now();
        let isVisible = !document.hidden;
        
        const updateFocusTime = () => {
            if (isVisible) {
                const currentTime = Date.now();
                const focusTime = currentTime - focusStartTime;
                
                if (!this.learningData.behaviors.focusTime[this.currentPageType]) {
                    this.learningData.behaviors.focusTime[this.currentPageType] = 0;
                }
                
                this.learningData.behaviors.focusTime[this.currentPageType] += focusTime;
            }
            focusStartTime = Date.now();
        };
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            updateFocusTime();
            isVisible = !document.hidden;
        });
        
        // 窗口焦点变化
        window.addEventListener('focus', () => {
            isVisible = true;
            focusStartTime = Date.now();
        });
        
        window.addEventListener('blur', () => {
            updateFocusTime();
            isVisible = false;
        });
    }
    
    // 记录正确/错误操作
    recordOperation(isCorrect, context = '') {
        if (isCorrect) {
            this.learningData.achievements.correctOperations++;
        } else {
            this.learningData.achievements.incorrectOperations++;
        }
        
        this.recordInteraction('operation', {
            correct: isCorrect,
            context: context,
            timestamp: Date.now()
        });
    }
    
    // 记录提示使用
    recordHintUsed(hintType = 'general') {
        this.learningData.achievements.hintsUsed++;
        this.recordInteraction('hint', {
            type: hintType,
            timestamp: Date.now()
        });
    }
    
    // 记录自我纠错
    recordSelfCorrection(context = '') {
        this.learningData.achievements.selfCorrections++;
        this.recordInteraction('self_correction', {
            context: context,
            timestamp: Date.now()
        });
    }
    
    // 分析学习模式
    analyzeLearningPatterns() {
        const data = this.learningData;
        
        // 分析学习风格
        const totalInteractions = data.behaviors.clickCount + data.behaviors.dragDropCount + data.behaviors.keyboardInputCount;
        const aiRatio = data.aiInteractions.totalQuestions / Math.max(totalInteractions, 1);
        
        if (aiRatio > 0.3) {
            data.patterns.learningStyle = 'interactive';
        } else if (data.behaviors.scrollDistance > 5000) {
            data.patterns.learningStyle = 'reading';
        } else if (data.behaviors.dragDropCount > 10) {
            data.patterns.learningStyle = 'visual';
        } else {
            data.patterns.learningStyle = 'mixed';
        }
        
        // 分析投入度
        const totalFocusTime = Object.values(data.behaviors.focusTime).reduce((sum, time) => sum + time, 0);
        const totalTime = Date.now() - data.startTime;
        const focusRatio = totalFocusTime / totalTime;
        
        if (focusRatio > 0.8) {
            data.patterns.engagementLevel = 'high';
        } else if (focusRatio > 0.5) {
            data.patterns.engagementLevel = 'medium';
        } else {
            data.patterns.engagementLevel = 'low';
        }
        
        // 分析求助行为
        if (data.aiInteractions.totalQuestions > 15) {
            data.patterns.helpSeekingBehavior = 'proactive';
        } else if (data.aiInteractions.totalQuestions > 5) {
            data.patterns.helpSeekingBehavior = 'reactive';
        } else {
            data.patterns.helpSeekingBehavior = 'independent';
        }
        
        // 分析坚持度
        const errorRate = data.achievements.incorrectOperations / Math.max(data.achievements.correctOperations + data.achievements.incorrectOperations, 1);
        const completionRate = Object.values(data.levels).filter(level => level.completed).length / 4;
        
        if (completionRate > 0.8 && errorRate < 0.2) {
            data.patterns.persistenceLevel = 'high';
        } else if (completionRate > 0.5) {
            data.patterns.persistenceLevel = 'medium';
        } else {
            data.patterns.persistenceLevel = 'low';
        }
        
        return data.patterns;
    }
    
    // 计算精准的学习指标
    calculatePreciseMetrics() {
        const data = this.learningData;
        const currentTime = Date.now();
        
        // 总学习时间（分钟）
        const totalTimeMinutes = Math.floor((currentTime - data.startTime) / 60000);
        
        // 有效学习时间（专注时间）
        const effectiveTime = Object.values(data.behaviors.focusTime).reduce((sum, time) => sum + time, 0);
        const effectiveTimeMinutes = Math.floor(effectiveTime / 60000);
        
        // 完成关卡数
        const completedLevels = Object.values(data.levels).filter(level => level.completed).length;
        
        // AI交互质量分数
        const avgQuestionQuality = data.aiInteractions.responseQuality.length > 0 
            ? data.aiInteractions.responseQuality.reduce((sum, q) => sum + q.quality, 0) / data.aiInteractions.responseQuality.length 
            : 0;
        
        // 操作准确率
        const totalOperations = data.achievements.correctOperations + data.achievements.incorrectOperations;
        const accuracy = totalOperations > 0 
            ? Math.round((data.achievements.correctOperations / totalOperations) * 100)
            : 0;
        
        // 学习效率（完成度/时间）
        const efficiency = totalTimeMinutes > 0 ? (completedLevels / totalTimeMinutes) * 60 : 0; // 关卡/小时
        
        // 主动性指标
        const proactivity = data.aiInteractions.totalQuestions + data.achievements.hintsUsed;
        
        return {
            totalTime: totalTimeMinutes,
            effectiveTime: effectiveTimeMinutes,
            completedLevels: completedLevels,
            aiInteractions: data.aiInteractions.totalQuestions,
            accuracy: accuracy,
            efficiency: Math.round(efficiency * 100) / 100,
            proactivity: proactivity,
            questionQuality: Math.round(avgQuestionQuality * 100) / 100,
            focusRatio: Math.round((effectiveTime / Math.max(currentTime - data.startTime, 1)) * 100),
            patterns: this.analyzeLearningPatterns()
        };
    }
    
    // 基于真实数据计算技能分数
    calculateSkillScores() {
        const data = this.learningData;
        const patterns = this.analyzeLearningPatterns();
        
        // 数据隐患识别能力
        const hazardScore = this.calculateHazardIdentificationScore(data);
        
        // 安全分享意识
        const sharingScore = this.calculateSafeSharingScore(data);
        
        // 数据备份技能
        const backupScore = this.calculateDataBackupScore(data);
        
        // 保护措施应用
        const protectionScore = this.calculateProtectionMeasuresScore(data);
        
        return {
            hazardIdentification: hazardScore,
            safeSharing: sharingScore,
            dataBackup: backupScore,
            protectionMeasures: protectionScore
        };
    }
    
    // 计算隐患识别分数
    calculateHazardIdentificationScore(data) {
        let score = 60; // 基础分
        
        // 完成关卡1加分
        if (data.levels.level1?.completed) score += 20;
        
        // 相关问题加分
        const hazardQuestions = data.aiInteractions.topicDistribution.hazardIdentification;
        score += Math.min(hazardQuestions * 2, 10);
        
        // 操作准确率加分
        const accuracy = data.achievements.correctOperations / Math.max(data.achievements.correctOperations + data.achievements.incorrectOperations, 1);
        score += Math.round(accuracy * 10);
        
        return Math.min(score, 100);
    }
    
    // 计算安全分享分数
    calculateSafeSharingScore(data) {
        let score = 60;
        
        if (data.levels.level2?.completed) score += 20;
        
        const sharingQuestions = data.aiInteractions.topicDistribution.safeSharing;
        score += Math.min(sharingQuestions * 2, 10);
        
        // 如果完成时间较短，说明理解较好
        if (data.levels.level2?.endTime && data.levels.level2?.startTime) {
            const duration = data.levels.level2.endTime - data.levels.level2.startTime;
            if (duration < 300000) score += 5; // 5分钟内完成加分
        }
        
        return Math.min(score, 100);
    }
    
    // 计算数据备份分数
    calculateDataBackupScore(data) {
        let score = 60;
        
        if (data.levels.level3?.completed) score += 20;
        
        const backupQuestions = data.aiInteractions.topicDistribution.dataBackup;
        score += Math.min(backupQuestions * 2, 10);
        
        // 右键操作次数（备份操作的关键）
        const rightClicks = data.behaviors.rightClickCount;
        score += Math.min(rightClicks, 10);
        
        return Math.min(score, 100);
    }
    
    // 计算保护措施分数
    calculateProtectionMeasuresScore(data) {
        let score = 60;
        
        if (data.levels.level4?.completed) score += 20;
        
        const protectionQuestions = data.aiInteractions.topicDistribution.protectionMeasures;
        score += Math.min(protectionQuestions * 2, 10);
        
        // 拖拽操作次数（匹配操作的关键）
        const dragDrops = data.behaviors.dragDropCount;
        score += Math.min(dragDrops, 10);
        
        return Math.min(score, 100);
    }
    
    // 保存数据
    saveData() {
        try {
            localStorage.setItem('enhanced_learning_data', JSON.stringify(this.learningData));
            localStorage.setItem('learning_metrics', JSON.stringify(this.calculatePreciseMetrics()));
            localStorage.setItem('skill_scores', JSON.stringify(this.calculateSkillScores()));
        } catch (error) {
            console.error('保存学习数据失败:', error);
        }
    }
    
    // 获取数据摘要（供评价系统使用）
    getDataSummary() {
        return this.calculatePreciseMetrics();
    }
    
    // 导出完整数据（供分析使用）
    exportData() {
        return {
            rawData: this.learningData,
            metrics: this.calculatePreciseMetrics(),
            skillScores: this.calculateSkillScores(),
            patterns: this.analyzeLearningPatterns()
        };
    }
}

// 全局初始化
if (typeof window !== 'undefined') {
    window.EnhancedLearningDataCollector = EnhancedLearningDataCollector;
    
    // 自动初始化（如果还没有实例）
    if (!window.enhancedDataCollector) {
        window.enhancedDataCollector = new EnhancedLearningDataCollector();
        console.log('增强版学习数据收集器已初始化');
    }
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedLearningDataCollector;
}

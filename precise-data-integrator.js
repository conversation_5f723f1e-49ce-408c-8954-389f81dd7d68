/**
 * 精确数据整合器
 * 确保AI评价系统各标签页数据的准确性和一致性
 */

class PreciseDataIntegrator {
    constructor() {
        this.dataCache = {};
        this.lastUpdateTime = 0;
        this.conversationData = [];
        this.realTimeMetrics = {
            aiInteractions: 0,
            effectiveTime: 0,
            accurateOperations: 0,
            totalOperations: 0,
            skillDemonstrations: {
                hazardIdentification: 0,
                safeSharing: 0,
                dataBackup: 0,
                protectionMeasures: 0
            }
        };
    }
    
    // 初始化数据整合器
    init() {
        this.loadStoredData();
        this.startRealTimeTracking();
        console.log('精确数据整合器已启动');
    }
    
    // 加载已存储的数据
    loadStoredData() {
        try {
            // 从localStorage加载基础数据
            const storedData = localStorage.getItem('precise_learning_data');
            if (storedData) {
                this.dataCache = JSON.parse(storedData);
            }
            
            // 加载对话数据
            const conversationData = localStorage.getItem('ai_conversation_data');
            if (conversationData) {
                this.conversationData = JSON.parse(conversationData);
            }
            
        } catch (error) {
            console.warn('加载存储数据失败:', error);
        }
    }
    
    // 开始实时追踪
    startRealTimeTracking() {
        // 监听AI对话
        this.trackAIConversations();
        
        // 监听学习进度
        this.trackLearningProgress();
        
        // 监听操作准确性
        this.trackOperationAccuracy();
        
        // 定期保存数据
        setInterval(() => {
            this.saveData();
        }, 30000); // 每30秒保存一次
    }
    
    // 追踪AI对话
    trackAIConversations() {
        // 重写全局的addMessage函数来追踪对话
        if (window.addMessage) {
            const originalAddMessage = window.addMessage;
            window.addMessage = (content, type) => {
                // 调用原函数
                originalAddMessage(content, type);
                
                // 记录对话数据
                if (type === 'user' || type === 'ai') {
                    this.recordConversation(content, type);
                }
            };
        }
        
        // 监听用户回答
        const originalSendMessage = window.sendMessage;
        if (originalSendMessage) {
            window.sendMessage = () => {
                const input = document.getElementById('userInput');
                if (input && input.value.trim()) {
                    this.analyzeUserResponse(input.value.trim());
                }
                originalSendMessage();
            };
        }
    }
    
    // 记录对话内容
    recordConversation(content, type) {
        const conversationEntry = {
            content: content,
            type: type,
            timestamp: Date.now(),
            wordCount: content.length
        };
        
        this.conversationData.push(conversationEntry);
        
        if (type === 'user') {
            this.realTimeMetrics.aiInteractions++;
            this.analyzeSkillDemonstration(content);
        }
        
        this.updateCache();
    }
    
    // 分析用户回答中的技能展示
    analyzeSkillDemonstration(userResponse) {
        const lowerResponse = userResponse.toLowerCase();
        
        // 数据隐患识别相关关键词
        const hazardKeywords = ['危险', '隐患', '风险', '陌生人', '链接', '二维码', '诈骗', '可疑', '不明'];
        if (hazardKeywords.some(keyword => lowerResponse.includes(keyword))) {
            this.realTimeMetrics.skillDemonstrations.hazardIdentification++;
        }
        
        // 安全分享相关关键词
        const sharingKeywords = ['朋友圈', '分享', '隐私', '位置', '照片', '个人信息', '公开', '权限'];
        if (sharingKeywords.some(keyword => lowerResponse.includes(keyword))) {
            this.realTimeMetrics.skillDemonstrations.safeSharing++;
        }
        
        // 数据备份相关关键词
        const backupKeywords = ['备份', '保存', '存储', '丢失', '重要', '文件', '云盘', 'u盘'];
        if (backupKeywords.some(keyword => lowerResponse.includes(keyword))) {
            this.realTimeMetrics.skillDemonstrations.dataBackup++;
        }
        
        // 保护措施相关关键词
        const protectionKeywords = ['保护', '措施', '密码', 'wifi', '安全', '防护', '设置', '权限'];
        if (protectionKeywords.some(keyword => lowerResponse.includes(keyword))) {
            this.realTimeMetrics.skillDemonstrations.protectionMeasures++;
        }
    }
    
    // 追踪学习进度
    trackLearningProgress() {
        // 监听localStorage变化来追踪关卡完成
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = (key, value) => {
            originalSetItem.call(localStorage, key, value);
            
            if (key.includes('level') && key.includes('completed')) {
                this.updateCompletionData();
            }
            
            if (key.includes('correct') || key.includes('incorrect')) {
                this.updateAccuracyData(key, value);
            }
        };
    }
    
    // 更新完成数据
    updateCompletionData() {
        let completedLevels = 0;
        for (let i = 1; i <= 5; i++) {
            if (localStorage.getItem(`level${i}_completed`) === 'true') {
                completedLevels++;
            }
        }
        this.realTimeMetrics.completedLevels = completedLevels;
        this.updateCache();
    }
    
    // 更新准确率数据
    updateAccuracyData(key, value) {
        if (key.includes('correct')) {
            this.realTimeMetrics.accurateOperations = parseInt(value) || 0;
        }
        
        // 计算总操作数
        const correctOps = parseInt(localStorage.getItem('correct_operations') || '0');
        const incorrectOps = parseInt(localStorage.getItem('incorrect_operations') || '0');
        this.realTimeMetrics.totalOperations = correctOps + incorrectOps;
        
        this.updateCache();
    }
    
    // 追踪操作准确性
    trackOperationAccuracy() {
        // 监听页面交互事件
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('drag-item') || 
                event.target.classList.contains('drop-zone') ||
                event.target.tagName === 'BUTTON') {
                this.recordInteraction(event.target);
            }
        });
    }
    
    // 记录交互行为
    recordInteraction(element) {
        const interactionData = {
            element: element.tagName,
            className: element.className,
            timestamp: Date.now()
        };
        
        // 简单的交互计数
        if (!this.dataCache.interactions) {
            this.dataCache.interactions = [];
        }
        this.dataCache.interactions.push(interactionData);
        
        // 限制存储的交互数量
        if (this.dataCache.interactions.length > 100) {
            this.dataCache.interactions = this.dataCache.interactions.slice(-50);
        }
    }
    
    // 更新缓存
    updateCache() {
        this.dataCache = {
            ...this.dataCache,
            ...this.realTimeMetrics,
            lastUpdate: Date.now()
        };
        this.lastUpdateTime = Date.now();
    }
    
    // 获取精确的数据摘要
    getAccurateDataSummary() {
        // 整合所有数据源
        const enhancedData = window.enhancedDataCollector ? 
            window.enhancedDataCollector.getDataSummary() : null;
        
        const baseData = this.getBaseMetrics();
        const conversationMetrics = this.getConversationMetrics();
        const skillMetrics = this.getSkillMetrics();
        
        return {
            // 基础数据
            completedLevels: enhancedData?.completedLevels || baseData.completedLevels || 0,
            totalTime: enhancedData?.totalTime || baseData.totalTime || this.calculateTotalTime(),
            effectiveTime: enhancedData?.effectiveTime || baseData.effectiveTime || this.calculateEffectiveTime(),
            
            // AI交互数据
            aiInteractions: this.realTimeMetrics.aiInteractions || conversationMetrics.totalInteractions,
            conversationQuality: conversationMetrics.averageResponseLength,
            
            // 准确率数据
            accuracy: this.calculateAccuracy(),
            operationAccuracy: this.calculateOperationAccuracy(),
            
            // 技能数据
            skillScores: skillMetrics,
            skillDemonstrations: this.realTimeMetrics.skillDemonstrations,
            
            // 行为数据
            engagementLevel: this.calculateEngagementLevel(),
            learningPattern: this.identifyLearningPattern(),
            focusRatio: enhancedData?.focusRatio || this.calculateFocusRatio(),
            
            // 元数据
            dataSource: 'precise-integrator',
            lastUpdate: this.lastUpdateTime,
            dataQuality: 'high'
        };
    }
    
    // 获取基础指标
    getBaseMetrics() {
        let completedLevels = 0;
        for (let i = 1; i <= 5; i++) {
            if (localStorage.getItem(`level${i}_completed`) === 'true') {
                completedLevels++;
            }
        }
        
        return {
            completedLevels: completedLevels,
            totalTime: this.calculateTotalTime(),
            effectiveTime: this.calculateEffectiveTime()
        };
    }
    
    // 获取对话指标
    getConversationMetrics() {
        const userResponses = this.conversationData.filter(item => item.type === 'user');
        
        if (userResponses.length === 0) {
            return {
                totalInteractions: 0,
                averageResponseLength: 0,
                totalWords: 0
            };
        }
        
        const totalWords = userResponses.reduce((sum, response) => sum + response.wordCount, 0);
        const averageLength = totalWords / userResponses.length;
        
        return {
            totalInteractions: userResponses.length,
            averageResponseLength: averageLength,
            totalWords: totalWords
        };
    }
    
    // 获取技能指标
    getSkillMetrics() {
        const demonstrations = this.realTimeMetrics.skillDemonstrations;
        const baseScore = 60;
        const maxBonus = 40;
        
        return {
            hazardIdentification: Math.min(baseScore + (demonstrations.hazardIdentification * 10), 100),
            safeSharing: Math.min(baseScore + (demonstrations.safeSharing * 10), 100),
            dataBackup: Math.min(baseScore + (demonstrations.dataBackup * 10), 100),
            protectionMeasures: Math.min(baseScore + (demonstrations.protectionMeasures * 10), 100)
        };
    }
    
    // 计算总学习时间
    calculateTotalTime() {
        const startTime = localStorage.getItem('learning_start_time');
        if (!startTime) return 0;
        
        const elapsed = Date.now() - parseInt(startTime);
        return Math.round(elapsed / 60000); // 转换为分钟
    }
    
    // 计算有效学习时间
    calculateEffectiveTime() {
        // 基于页面可见性和交互频率计算
        const totalTime = this.calculateTotalTime();
        const interactionCount = this.dataCache.interactions?.length || 0;
        
        // 简单的有效时间估算：基于交互密度
        const effectiveRatio = Math.min(interactionCount / 50, 0.8); // 最多80%有效
        return Math.round(totalTime * effectiveRatio);
    }
    
    // 计算准确率
    calculateAccuracy() {
        const correct = parseInt(localStorage.getItem('correct_operations') || '0');
        const incorrect = parseInt(localStorage.getItem('incorrect_operations') || '0');
        const total = correct + incorrect;
        
        if (total === 0) return 60; // 默认值
        
        return Math.round((correct / total) * 100);
    }
    
    // 计算操作准确率
    calculateOperationAccuracy() {
        if (this.realTimeMetrics.totalOperations === 0) return 60;
        
        return Math.round((this.realTimeMetrics.accurateOperations / this.realTimeMetrics.totalOperations) * 100);
    }
    
    // 计算参与度
    calculateEngagementLevel() {
        const interactions = this.realTimeMetrics.aiInteractions;
        const timeSpent = this.calculateTotalTime();
        
        if (timeSpent === 0) return 'medium';
        
        const interactionRate = interactions / (timeSpent / 5); // 每5分钟的交互次数
        
        if (interactionRate > 2) return 'high';
        if (interactionRate > 1) return 'medium';
        return 'low';
    }
    
    // 识别学习模式
    identifyLearningPattern() {
        const conversationMetrics = this.getConversationMetrics();
        const avgResponseLength = conversationMetrics.averageResponseLength;
        
        if (avgResponseLength > 30) return 'detailed';
        if (avgResponseLength > 15) return 'balanced';
        return 'concise';
    }
    
    // 计算专注度比例
    calculateFocusRatio() {
        const effectiveTime = this.calculateEffectiveTime();
        const totalTime = this.calculateTotalTime();
        
        if (totalTime === 0) return 75;
        
        return Math.round((effectiveTime / totalTime) * 100);
    }
    
    // 保存数据
    saveData() {
        try {
            localStorage.setItem('precise_learning_data', JSON.stringify(this.dataCache));
            localStorage.setItem('ai_conversation_data', JSON.stringify(this.conversationData));
            localStorage.setItem('realtime_metrics', JSON.stringify(this.realTimeMetrics));
        } catch (error) {
            console.warn('保存精确数据失败:', error);
        }
    }
    
    // 生成详细的学习报告
    generateDetailedReport() {
        const summary = this.getAccurateDataSummary();
        const conversationMetrics = this.getConversationMetrics();
        
        return {
            basicMetrics: {
                completedLevels: summary.completedLevels,
                totalTime: `${summary.totalTime}分钟`,
                effectiveTime: `${summary.effectiveTime}分钟`,
                aiInteractions: summary.aiInteractions,
                accuracy: `${summary.accuracy}%`
            },
            
            skillAnalysis: {
                scores: summary.skillScores,
                demonstrations: summary.skillDemonstrations,
                strengths: this.identifyStrengths(summary.skillScores),
                improvements: this.identifyImprovements(summary.skillScores)
            },
            
            behaviorAnalysis: {
                engagementLevel: summary.engagementLevel,
                learningPattern: summary.learningPattern,
                focusRatio: summary.focusRatio,
                interactionStyle: this.analyzeInteractionStyle(conversationMetrics)
            },
            
            recommendations: this.generateRecommendations(summary),
            
            metadata: {
                dataSource: summary.dataSource,
                dataQuality: summary.dataQuality,
                lastUpdate: new Date(summary.lastUpdate).toLocaleString()
            }
        };
    }
    
    // 识别优势
    identifyStrengths(skillScores) {
        const strengths = [];
        Object.entries(skillScores).forEach(([skill, score]) => {
            if (score >= 80) {
                const skillNames = {
                    hazardIdentification: '数据隐患识别',
                    safeSharing: '安全分享意识',
                    dataBackup: '数据备份技能',
                    protectionMeasures: '保护措施应用'
                };
                strengths.push(skillNames[skill]);
            }
        });
        return strengths.length > 0 ? strengths : ['积极参与学习'];
    }
    
    // 识别需要改进的地方
    identifyImprovements(skillScores) {
        const improvements = [];
        Object.entries(skillScores).forEach(([skill, score]) => {
            if (score < 70) {
                const skillNames = {
                    hazardIdentification: '数据隐患识别能力',
                    safeSharing: '安全分享意识',
                    dataBackup: '数据备份技能',
                    protectionMeasures: '保护措施应用'
                };
                improvements.push(`加强${skillNames[skill]}`);
            }
        });
        return improvements.length > 0 ? improvements : ['继续保持学习积极性'];
    }
    
    // 分析交互风格
    analyzeInteractionStyle(conversationMetrics) {
        if (conversationMetrics.averageResponseLength > 30) {
            return '详细型：喜欢深入思考和详细表达';
        } else if (conversationMetrics.averageResponseLength > 15) {
            return '平衡型：能够适度表达想法';
        } else {
            return '简洁型：倾向于简明扼要的回答';
        }
    }
    
    // 生成建议
    generateRecommendations(summary) {
        const recommendations = [];
        
        // 基于技能分数的建议
        Object.entries(summary.skillScores).forEach(([skill, score]) => {
            if (score < 70) {
                const suggestions = {
                    hazardIdentification: '建议多关注网络安全新闻，提高识别隐患的能力',
                    safeSharing: '建议练习社交媒体隐私设置，增强分享安全意识',
                    dataBackup: '建议定期练习文件备份操作，养成备份习惯',
                    protectionMeasures: '建议学习更多保护措施，如密码管理等'
                };
                recommendations.push(suggestions[skill]);
            }
        });
        
        // 基于参与度的建议
        if (summary.engagementLevel === 'low') {
            recommendations.push('建议增加学习互动，多提问和思考');
        }
        
        // 基于专注度的建议
        if (summary.focusRatio < 60) {
            recommendations.push('建议在学习时减少干扰，提高专注度');
        }
        
        return recommendations.length > 0 ? recommendations : ['继续保持良好的学习状态'];
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.preciseDataIntegrator = new PreciseDataIntegrator();
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = PreciseDataIntegrator;
}
